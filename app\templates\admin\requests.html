{% extends "base.html" %}

{% block title %}إدارة الطلبات - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-text"></i>
        إدارة الطلبات
    </h2>
    <div>
        <div class="btn-group me-2" role="group">
            <a href="/admin/requests" class="btn btn-outline-primary {% if not current_status %}active{% endif %}">
                الكل
            </a>
            {% for status in statuses %}
            <a href="/admin/requests?status={{ status }}"
               class="btn btn-outline-primary {% if current_status == status %}active{% endif %}">
                {% if status == 'pending' %}قيد المراجعة
                {% elif status == 'in_progress' %}قيد التنفيذ
                {% elif status == 'completed' %}مكتملة
                {% elif status == 'rejected' %}مرفوضة
                {% endif %}
            </a>
            {% endfor %}
        </div>
        <a href="/admin/archived-requests" class="btn btn-outline-warning">
            <i class="bi bi-archive"></i>
            الطلبات المؤرشفة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if requests %}
        <!-- Bulk Actions -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-body py-3">
                <form method="post" action="/admin/requests/bulk-action" id="bulkRequestForm">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <select class="form-select" name="action" required>
                                <option value="">اختر العملية</option>
                                <option value="pending">تحويل إلى قيد المراجعة</option>
                                <option value="in_progress">تحويل إلى قيد التنفيذ</option>
                                <option value="completed">تحويل إلى مكتمل</option>
                                <option value="rejected">تحويل إلى مرفوض</option>
                                <option value="archive">أرشفة المحدد</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary" id="bulkRequestActionBtn" disabled>
                                <i class="bi bi-gear"></i>
                                تطبيق على المحدد
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <span id="selectedRequestCount">0</span> طلب محدد
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAllRequests" class="form-check-input">
                        </th>
                        <th>رقم الطلب</th>
                        <th>العنوان</th>
                        <th>مقدم الطلب</th>
                        <th>الحالة</th>
                        <th>المرفقات</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in requests %}
                    <tr>
                        <td>
                            <input type="checkbox" name="request_ids" value="{{ req.id }}"
                                   class="form-check-input request-checkbox" form="bulkRequestForm">
                        </td>
                        <td>
                            <code>{{ req.request_number }}</code>
                        </td>
                        <td>{{ req.request_title }}</td>
                        <td>{{ req.user.full_name }}</td>
                        <td>
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ req.files|length }}</span>
                        </td>
                        <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary me-1">
                                <i class="bi bi-eye"></i>
                            </a>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="pending">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-clock text-warning"></i> قيد المراجعة
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-check-circle text-success"></i> مكتمل
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                                            </button>
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/archive" class="d-inline">
                                            <button type="submit" class="dropdown-item text-warning"
                                                    onclick="return confirm('هل أنت متأكد من أرشفة هذا الطلب؟')">
                                                <i class="bi bi-archive text-warning"></i> أرشفة الطلب
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-text display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا توجد طلبات</h4>
            {% if current_status %}
            <p class="text-muted">لا توجد طلبات بحالة "{{ current_status }}"</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllRequests');
    const requestCheckboxes = document.querySelectorAll('.request-checkbox');
    const selectedCountSpan = document.getElementById('selectedRequestCount');
    const bulkActionBtn = document.getElementById('bulkRequestActionBtn');
    const bulkForm = document.getElementById('bulkRequestForm');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            requestCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }

    // Individual checkbox change
    requestCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // Update select all checkbox state
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.request-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === requestCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < requestCheckboxes.length;
            }
        });
    });

    // Update selected count and button state
    function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.request-checkbox:checked').length;
        if (selectedCountSpan) selectedCountSpan.textContent = checkedCount;
        if (bulkActionBtn) bulkActionBtn.disabled = checkedCount === 0;
    }

    // Form submission confirmation
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.request-checkbox:checked').length;
            const action = this.querySelector('select[name="action"]').value;

            if (checkedCount === 0) {
                e.preventDefault();
                alert('يرجى اختيار طلب واحد على الأقل');
                return;
            }

            const actionNames = {
                'pending': 'تحويل إلى قيد المراجعة',
                'in_progress': 'تحويل إلى قيد التنفيذ',
                'completed': 'تحويل إلى مكتمل',
                'rejected': 'تحويل إلى مرفوض',
                'archive': 'أرشفة'
            };

            const actionName = actionNames[action] || action;

            if (!confirm(`هل أنت متأكد من ${actionName} ${checkedCount} طلب؟`)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            bulkActionBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التطبيق...';
            bulkActionBtn.disabled = true;
        });
    }

    // Initialize
    updateSelectedCount();
});
</script>
{% endblock %}
