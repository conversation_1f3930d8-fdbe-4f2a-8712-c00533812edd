#!/usr/bin/env python3
"""
Test script to verify the basic setup of CMSVS Internal System
Run this script to check if the basic structure is correct
"""

import os
import sys
from pathlib import Path

def test_file_structure():
    """Test if all required files and directories exist"""
    print("🔍 Testing file structure...")
    
    required_files = [
        "app/__init__.py",
        "app/main.py",
        "app/config.py",
        "app/database.py",
        "app/models/__init__.py",
        "app/models/user.py",
        "app/models/request.py",
        "app/models/file.py",
        "app/models/activity.py",
        "app/routes/__init__.py",
        "app/routes/auth.py",
        "app/routes/dashboard.py",
        "app/routes/admin.py",
        "app/services/__init__.py",
        "app/services/user_service.py",
        "app/services/request_service.py",
        "app/utils/__init__.py",
        "app/utils/auth.py",
        "app/utils/file_handler.py",
        "app/templates/base.html",
        "app/templates/auth/login.html",
        "app/templates/auth/register.html",
        "app/templates/dashboard/user_dashboard.html",
        "app/templates/requests/new_request.html",
        "app/templates/requests/view_request.html",
        "app/templates/admin/dashboard.html",
        "app/templates/errors/404.html",
        "requirements.txt",
        ".env.example",
        "run.py",
        "init_db.py",
        "README.md"
    ]
    
    required_dirs = [
        "app",
        "app/models",
        "app/routes", 
        "app/services",
        "app/utils",
        "app/templates",
        "app/templates/auth",
        "app/templates/dashboard",
        "app/templates/requests",
        "app/templates/admin",
        "app/templates/errors",
        "app/templates/profile"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Check directories
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"✅ Directory: {dir_path}")
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ File: {file_path}")
    
    if missing_dirs:
        print("\n❌ Missing directories:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
    
    if missing_files:
        print("\n❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def test_python_syntax():
    """Test if Python files have valid syntax"""
    print("\n🐍 Testing Python syntax...")
    
    python_files = []
    for root, dirs, files in os.walk("app"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    # Add root level Python files
    root_python_files = ["run.py", "init_db.py", "test_setup.py"]
    for file in root_python_files:
        if os.path.exists(file):
            python_files.append(file)
    
    syntax_errors = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            print(f"✅ Syntax OK: {file_path}")
        except SyntaxError as e:
            syntax_errors.append((file_path, str(e)))
            print(f"❌ Syntax Error: {file_path} - {e}")
        except Exception as e:
            print(f"⚠️  Warning: {file_path} - {e}")
    
    return len(syntax_errors) == 0

def test_imports():
    """Test if basic imports work"""
    print("\n📦 Testing basic imports...")
    
    try:
        # Test if we can import the main modules
        sys.path.insert(0, os.getcwd())
        
        print("✅ Testing app.config...")
        from app.config import settings
        
        print("✅ Testing app.database...")
        # Don't actually connect, just import
        import app.database
        
        print("✅ Testing app.models...")
        from app.models import User, Request, File, Activity
        
        print("✅ Testing app.services...")
        from app.services.user_service import UserService
        from app.services.request_service import RequestService
        
        print("✅ Testing app.utils...")
        from app.utils.auth import get_password_hash
        from app.utils.file_handler import FileHandler
        
        print("✅ All basic imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_configuration():
    """Test configuration"""
    print("\n⚙️  Testing configuration...")
    
    try:
        from app.config import settings
        
        print(f"✅ App Name: {settings.app_name}")
        print(f"✅ App Version: {settings.app_version}")
        print(f"✅ Debug Mode: {settings.debug}")
        print(f"✅ Upload Directory: {settings.upload_directory}")
        print(f"✅ Max File Size: {settings.max_file_size} bytes")
        print(f"✅ Allowed File Types: {settings.allowed_file_types_list}")
        
        # Check if upload directory exists or can be created
        if not os.path.exists(settings.upload_directory):
            os.makedirs(settings.upload_directory, exist_ok=True)
            print(f"✅ Created upload directory: {settings.upload_directory}")
        else:
            print(f"✅ Upload directory exists: {settings.upload_directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 CMSVS Internal System - Setup Test")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Syntax", test_python_syntax),
        ("Basic Imports", test_imports),
        ("Configuration", test_configuration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The basic setup looks good.")
        print("\n📝 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Set up PostgreSQL database")
        print("3. Configure .env file")
        print("4. Run: python init_db.py")
        print("5. Start application: python run.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before proceeding.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
