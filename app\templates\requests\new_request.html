{% extends "base.html" %}

{% block title %}طلب جديد - CMSVS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-plus-circle"></i>
                    إنشاء طلب أرشفة جديد
                </h4>
            </div>
            <div class="card-body">
                <form method="post" action="/requests/new" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="request_name" class="form-label">اسم الطلب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="request_name" name="request_name" required
                                       placeholder="مثال: أرشفة وثائق المشروع">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="request_title" class="form-label">عنوان الطلب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="request_title" name="request_title" required
                                       placeholder="مثال: أرشفة وثائق مشروع التطوير الجديد">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الطلب</label>
                        <textarea class="form-control" id="description" name="description" rows="4"
                                  placeholder="اكتب وصفاً تفصيلياً للطلب والوثائق المراد أرشفتها..."></textarea>
                    </div>

                    <!-- File Upload Section -->
                    <div class="mb-4">
                        <label class="form-label">المرفقات (اختياري - حد أقصى 5 ملفات)</label>
                        <div class="file-upload-area" onclick="document.getElementById('files').click()">
                            <i class="bi bi-cloud-upload display-4 text-muted"></i>
                            <p class="mt-2 mb-0">اضغط هنا لاختيار الملفات</p>
                            <p class="small text-muted">أو اسحب الملفات وأفلتها هنا</p>
                            <p class="small text-muted">
                                الأنواع المسموحة: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF
                                <br>
                                الحد الأقصى لحجم الملف: 10 ميجابايت
                            </p>
                        </div>
                        <input type="file" class="form-control d-none" id="files" name="files" multiple
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                               onchange="previewFiles(this)">
                        
                        <!-- File Preview -->
                        <div id="file-preview" class="mt-3"></div>
                    </div>

                    <!-- Auto-generated Fields Info -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle"></i>
                            معلومات إضافية
                        </h6>
                        <p class="mb-0">سيتم إنشاء رقم الطلب والرمز التعريفي الفريد تلقائياً عند إرسال الطلب.</p>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="/dashboard" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i>
                            إرسال الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Drag and drop functionality
const uploadArea = document.querySelector('.file-upload-area');
const fileInput = document.getElementById('files');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, preventDefaults, false);
});

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, unhighlight, false);
});

function highlight(e) {
    uploadArea.style.borderColor = '#007bff';
    uploadArea.style.backgroundColor = '#f8f9fa';
}

function unhighlight(e) {
    uploadArea.style.borderColor = '#ccc';
    uploadArea.style.backgroundColor = 'transparent';
}

uploadArea.addEventListener('drop', handleDrop, false);

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    fileInput.files = files;
    previewFiles(fileInput);
}

// File validation
fileInput.addEventListener('change', function() {
    const files = this.files;
    const maxFiles = 5;
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];
    
    if (files.length > maxFiles) {
        alert(`يمكنك رفع ${maxFiles} ملفات كحد أقصى`);
        this.value = '';
        return;
    }
    
    for (let file of files) {
        const fileExt = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExt)) {
            alert(`نوع الملف ${fileExt} غير مسموح`);
            this.value = '';
            return;
        }
        
        if (file.size > maxSize) {
            alert(`حجم الملف ${file.name} يتجاوز الحد المسموح (10 ميجابايت)`);
            this.value = '';
            return;
        }
    }
});
</script>
{% endblock %}
