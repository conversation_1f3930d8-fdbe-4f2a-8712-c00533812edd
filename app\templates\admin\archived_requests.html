{% extends "base.html" %}

{% block title %}الطلبات المؤرشفة - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-archive"></i>
        الطلبات المؤرشفة
    </h2>
    <div>
        <a href="/admin/requests" class="btn btn-primary">
            <i class="bi bi-arrow-left"></i>
            العودة للطلبات النشطة
        </a>
    </div>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-transparent border-0 py-4">
        <h4 class="mb-1 fw-bold text-dark">
            <i class="bi bi-archive text-warning me-2"></i>
            الطلبات المؤرشفة
        </h4>
        <p class="text-muted mb-0 small">الطلبات التي تم أرشفتها من النظام</p>
    </div>
    <div class="card-body p-0">
        {% if requests %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="border-0 fw-semibold text-dark ps-4">رقم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">العنوان</th>
                        <th class="border-0 fw-semibold text-dark">مقدم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">الحالة</th>
                        <th class="border-0 fw-semibold text-dark">تاريخ الأرشفة</th>
                        <th class="border-0 fw-semibold text-dark pe-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in requests %}
                    <tr class="border-0">
                        <td class="ps-4 py-3">
                            <span class="badge bg-light text-dark fw-semibold px-3 py-2">{{ req.request_number }}</span>
                        </td>
                        <td class="py-3">
                            <div class="fw-semibold text-dark">{{ req.request_title }}</div>
                        </td>
                        <td class="py-3">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-secondary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="bi bi-person text-white small"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold text-dark">{{ req.user.full_name }}</div>
                                    <div class="text-muted small">{{ req.user.email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="py-3">
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning bg-gradient text-dark fw-semibold px-3 py-2">
                                <i class="bi bi-clock me-1"></i>
                                قيد المراجعة
                            </span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info bg-gradient text-white fw-semibold px-3 py-2">
                                <i class="bi bi-play-circle me-1"></i>
                                قيد التنفيذ
                            </span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success bg-gradient text-white fw-semibold px-3 py-2">
                                <i class="bi bi-check-circle me-1"></i>
                                مكتمل
                            </span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger bg-gradient text-white fw-semibold px-3 py-2">
                                <i class="bi bi-x-circle me-1"></i>
                                مرفوض
                            </span>
                            {% endif %}
                            <div class="mt-1">
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-archive me-1"></i>
                                    مؤرشف
                                </span>
                            </div>
                        </td>
                        <td class="py-3">
                            <div class="text-dark fw-semibold">{{ req.updated_at.strftime('%Y-%m-%d') if req.updated_at else req.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="text-muted small">{{ req.updated_at.strftime('%H:%M') if req.updated_at else req.created_at.strftime('%H:%M') }}</div>
                        </td>
                        <td class="pe-4 py-3">
                            <div class="d-flex gap-2">
                                <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary px-3">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض
                                </a>
                                <form method="post" action="/admin/requests/{{ req.id }}/restore" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-outline-success px-3"
                                            onclick="return confirm('هل أنت متأكد من استعادة هذا الطلب؟')">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        استعادة
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="avatar-lg bg-light rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                <i class="bi bi-archive fs-3 text-muted"></i>
            </div>
            <h5 class="text-muted fw-semibold">لا توجد طلبات مؤرشفة</h5>
            <p class="text-muted mb-0">لم يتم أرشفة أي طلبات حتى الآن</p>
        </div>
        {% endif %}
    </div>
</div>

{% if requests %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-4">
                <i class="bi bi-info-circle text-info fs-3 mb-3"></i>
                <h6 class="fw-bold text-dark">معلومات الأرشفة</h6>
                <p class="text-muted small mb-0">
                    الطلبات المؤرشفة لا تظهر في القوائم العادية ولكن يمكن استعادتها في أي وقت
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-4">
                <i class="bi bi-shield-check text-success fs-3 mb-3"></i>
                <h6 class="fw-bold text-dark">الأمان والخصوصية</h6>
                <p class="text-muted small mb-0">
                    جميع البيانات والملفات محفوظة بأمان ويمكن الوصول إليها عند الحاجة
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation for restore actions
    const restoreForms = document.querySelectorAll('form[action*="/restore"]');
    restoreForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>جاري الاستعادة...';
            button.disabled = true;
        });
    });
});
</script>
{% endblock %}
