{% extends "base.html" %}

{% block title %}إدارة المستخدمين - CMSVS{% endblock %}

{% block extra_css %}
<style>
    /* Sneat Admin Template Inspired Styles */
    :root {
        --sneat-primary: #696cff;
        --sneat-primary-light: rgba(105, 108, 255, 0.16);
        --sneat-success: #71dd37;
        --sneat-success-light: rgba(113, 221, 55, 0.16);
        --sneat-warning: #ffb400;
        --sneat-warning-light: rgba(255, 180, 0, 0.16);
        --sneat-danger: #ff3e1d;
        --sneat-danger-light: rgba(255, 62, 29, 0.16);
        --sneat-info: #03c3ec;
        --sneat-info-light: rgba(3, 195, 236, 0.16);
        --sneat-secondary: #8592a3;
        --sneat-secondary-light: rgba(133, 146, 163, 0.16);
        --sneat-dark: #233446;
        --sneat-light: #f5f5f9;
        --sneat-border: #d9dee3;
        --sneat-text-muted: #a1acb8;
        --sneat-text-heading: #566a7f;
        --sneat-bg-body: #f5f5f9;
        --sneat-card-bg: #ffffff;
        --sneat-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
        --sneat-shadow-hover: 0 4px 12px 0 rgba(67, 89, 113, 0.16);
    }

    /* Page Layout */
    body {
        background-color: var(--sneat-bg-body);
        font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    }

    .container-xxl {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    /* Avatar Styles */
    .avatar {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        border-radius: 50%;
        background-color: var(--sneat-light);
        color: var(--sneat-text-heading);
        font-weight: 500;
        flex-shrink: 0;
    }

    .avatar-sm {
        width: 38px;
        height: 38px;
        font-size: 0.875rem;
    }

    .avatar-xs {
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    /* Card Styles */
    .card {
        background: var(--sneat-card-bg);
        border: 1px solid var(--sneat-border);
        border-radius: 0.5rem;
        box-shadow: var(--sneat-shadow);
        transition: all 0.2s ease-in-out;
        margin-bottom: 1.5rem;
    }

    .card:hover {
        box-shadow: var(--sneat-shadow-hover);
    }

    .card-header {
        background: transparent;
        border-bottom: 1px solid var(--sneat-border);
        padding: 1.5rem 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 0 1.5rem 1.5rem;
    }

    .card-title {
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--sneat-text-heading);
    }

    .card-subtitle {
        font-size: 0.875rem;
        color: var(--sneat-text-muted);
        margin-bottom: 0;
    }

    /* Button Styles */
    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        padding: 0.4375rem 1rem;
        transition: all 0.2s ease-in-out;
        border: 1px solid transparent;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
    }

    .btn-primary {
        background-color: var(--sneat-primary);
        border-color: var(--sneat-primary);
        color: #fff;
    }

    .btn-primary:hover {
        background-color: #5f61e6;
        border-color: #5f61e6;
        color: #fff;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(105, 108, 255, 0.4);
    }

    .btn-outline-primary {
        color: var(--sneat-primary);
        border-color: var(--sneat-primary);
        background: transparent;
    }

    .btn-outline-primary:hover {
        background-color: var(--sneat-primary);
        border-color: var(--sneat-primary);
        color: #fff;
    }

    .btn-outline-success {
        color: var(--sneat-success);
        border-color: var(--sneat-success);
        background: transparent;
    }

    .btn-outline-success:hover {
        background-color: var(--sneat-success);
        border-color: var(--sneat-success);
        color: #fff;
    }

    .btn-outline-warning {
        color: var(--sneat-warning);
        border-color: var(--sneat-warning);
        background: transparent;
    }

    .btn-outline-warning:hover {
        background-color: var(--sneat-warning);
        border-color: var(--sneat-warning);
        color: #fff;
    }

    .btn-outline-secondary {
        color: var(--sneat-secondary);
        border-color: var(--sneat-border);
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background-color: var(--sneat-secondary);
        border-color: var(--sneat-secondary);
        color: #fff;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8125rem;
        border-radius: 0.25rem;
    }

    .btn-icon {
        width: 32px;
        height: 32px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .hide-arrow::after {
        display: none !important;
    }

    /* Form Controls */
    .form-control, .form-select {
        border: 1px solid var(--sneat-border);
        border-radius: 0.375rem;
        padding: 0.4375rem 0.875rem;
        font-size: 0.875rem;
        color: var(--sneat-text-heading);
        background-color: var(--sneat-card-bg);
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--sneat-primary);
        box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
        outline: 0;
    }

    /* Dropdown Styles */
    .dropdown-menu {
        border: 1px solid var(--sneat-border);
        border-radius: 0.5rem;
        box-shadow: 0 0.25rem 1rem rgba(161, 172, 184, 0.45);
        padding: 0.5rem;
        margin-top: 0.25rem;
        min-width: 160px;
    }

    .dropdown-item {
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        color: var(--sneat-text-heading);
        transition: all 0.2s ease-in-out;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
    }

    .dropdown-item:hover {
        background-color: var(--sneat-primary-light);
        color: var(--sneat-primary);
    }

    .dropdown-item i {
        width: 1rem;
        text-align: center;
        font-size: 0.875rem;
    }

    .dropdown-divider {
        margin: 0.5rem 0;
        border-color: var(--sneat-border);
    }

    /* Badge Styles */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        white-space: nowrap;
        text-transform: capitalize;
    }

    .badge-primary {
        background-color: var(--sneat-primary-light);
        color: var(--sneat-primary);
    }

    .badge-success {
        background-color: var(--sneat-success-light);
        color: var(--sneat-success);
    }

    .badge-warning {
        background-color: var(--sneat-warning-light);
        color: var(--sneat-warning);
    }

    .badge-secondary {
        background-color: var(--sneat-secondary-light);
        color: var(--sneat-secondary);
    }

    .badge-info {
        background-color: var(--sneat-info-light);
        color: var(--sneat-info);
    }

    .badge-light-secondary {
        background-color: rgba(133, 146, 163, 0.12);
        color: var(--sneat-secondary);
    }

    /* Table Styles */
    .users-table-container {
        width: 100%;
        overflow: hidden;
        background: var(--sneat-card-bg);
    }

    .users-table {
        width: 100%;
        table-layout: fixed;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .users-table th {
        background-color: transparent;
        border-bottom: 1px solid var(--sneat-border);
        padding: 1rem 0.75rem;
        font-weight: 500;
        color: var(--sneat-text-muted);
        font-size: 0.8125rem;
        text-transform: uppercase;
        letter-spacing: 0.17px;
        vertical-align: middle;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
    }

    .users-table th:first-child {
        padding-left: 1.5rem;
    }

    .users-table th:last-child {
        padding-right: 1.5rem;
    }

    .users-table td {
        border-bottom: 1px solid var(--sneat-border);
        padding: 1rem 0.75rem;
        vertical-align: middle;
        color: var(--sneat-text-heading);
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .users-table td:first-child {
        padding-left: 1.5rem;
    }

    .users-table td:last-child {
        padding-right: 1.5rem;
    }

    .users-table tbody tr {
        transition: background-color 0.2s ease-in-out;
    }

    .users-table tbody tr:hover {
        background-color: rgba(105, 108, 255, 0.04);
    }

    /* Responsive Column Widths */
    .col-checkbox { width: 4%; }
    .col-id { width: 6%; }
    .col-username { width: 15%; }
    .col-fullname { width: 18%; }
    .col-email { width: 20%; }
    .col-role { width: 10%; }
    .col-status { width: 9%; }
    .col-date { width: 10%; }
    .col-actions { width: 8%; }

    /* User Info Container */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 0;
        flex: 1;
    }

    .user-info-text {
        min-width: 0;
        flex: 1;
    }

    .user-info-text .username {
        font-weight: 600;
        color: var(--sneat-text-heading);
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 0.125rem;
    }

    .user-info-text .current-user-badge {
        font-size: 0.6875rem;
        color: var(--sneat-primary);
        font-weight: 500;
    }

    /* Action Buttons */
    .action-buttons-container {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
    }

    .btn-action {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 0.375rem;
        min-width: auto;
        white-space: nowrap;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
    }

    .btn-action i {
        font-size: 0.875rem;
    }

    .btn-action:hover {
        transform: translateY(-1px);
    }

    /* Dropdown Styles */
    .dropdown-menu {
        border: 1px solid var(--sneat-border);
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(67, 89, 113, 0.12);
        z-index: 1050;
        min-width: 180px;
        padding: 0.5rem;
        background: var(--sneat-card-bg);
    }

    .dropdown-item {
        border-radius: 0.375rem;
        margin: 0.125rem 0;
        transition: all 0.2s ease-in-out;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        color: var(--sneat-text-heading);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dropdown-item:hover {
        background-color: var(--sneat-primary-light);
        color: var(--sneat-primary);
        transform: translateX(-2px);
    }

    .dropdown-item i {
        width: 1rem;
        text-align: center;
    }

    /* Content Styling */
    .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }

    /* User Info Styles */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .avatar {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .avatar-sm {
        width: 38px;
        height: 38px;
        font-size: 0.875rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .user-info-text {
        flex: 1;
        min-width: 0;
    }

    .username {
        font-weight: 500;
        color: var(--sneat-text-heading);
        font-size: 0.875rem;
        line-height: 1.2;
        margin-bottom: 0.125rem;
    }

    .user-email {
        color: var(--sneat-text-muted);
        font-size: 0.8125rem;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .current-user-badge {
        font-size: 0.6875rem;
        color: var(--sneat-primary);
        font-weight: 500;
        margin-top: 0.125rem;
    }

    .fullname-text, .email-text {
        color: var(--sneat-text-heading);
        font-size: 0.875rem;
    }

    .email-text {
        font-size: 0.8125rem;
        color: var(--sneat-text-muted);
        font-weight: 400;
    }

    .fullname-text {
        font-size: 0.875rem;
        color: var(--sneat-text-heading);
        font-weight: 500;
    }

    /* Form Check Styles */
    .form-check-input {
        width: 1.125rem;
        height: 1.125rem;
        border: 1px solid var(--sneat-border);
        border-radius: 0.25rem;
        background-color: var(--sneat-card-bg);
        transition: all 0.2s ease-in-out;
    }

    .form-check-input:checked {
        background-color: var(--sneat-primary);
        border-color: var(--sneat-primary);
    }

    .form-check-input:focus {
        border-color: var(--sneat-primary);
        box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
    }

    /* Responsive Design - No Horizontal Scroll */

    /* Large Desktop (1200px+) */
    @media (min-width: 1200px) {
        .col-checkbox { width: 4%; }
        .col-id { width: 6%; }
        .col-username { width: 15%; }
        .col-fullname { width: 18%; }
        .col-email { width: 20%; }
        .col-role { width: 10%; }
        .col-status { width: 9%; }
        .col-date { width: 10%; }
        .col-actions { width: 8%; }

        .btn-action .btn-text { display: inline; }
    }

    /* Desktop (992px - 1199px) */
    @media (max-width: 1199px) and (min-width: 992px) {
        .col-checkbox { width: 4%; }
        .col-id { width: 6%; }
        .col-username { width: 16%; }
        .col-fullname { width: 18%; }
        .col-email { width: 22%; }
        .col-role { width: 10%; }
        .col-status { width: 9%; }
        .col-date { width: 8%; }
        .col-actions { width: 7%; }

        .users-table th,
        .users-table td {
            padding: 0.6rem 0.4rem;
            font-size: 0.8rem;
        }

        .btn-action .btn-text { display: none; }
        .btn-action { padding: 0.35rem 0.5rem; }
    }

    /* Tablet (768px - 991px) */
    @media (max-width: 991px) and (min-width: 768px) {
        .col-checkbox { width: 5%; }
        .col-id { width: 7%; }
        .col-username { width: 18%; }
        .col-fullname { width: 0%; } /* Hide on tablet */
        .col-email { width: 25%; }
        .col-role { width: 12%; }
        .col-status { width: 11%; }
        .col-date { width: 12%; }
        .col-actions { width: 10%; }

        .hide-tablet { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.5rem 0.3rem;
            font-size: 0.75rem;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }

        .btn-action {
            padding: 0.3rem 0.4rem;
            font-size: 0.65rem;
        }

        .btn-action .btn-text { display: none; }

        .badge {
            font-size: 0.65rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* Mobile (576px - 767px) */
    @media (max-width: 767px) and (min-width: 576px) {
        .col-checkbox { width: 6%; }
        .col-id { width: 8%; }
        .col-username { width: 22%; }
        .col-fullname { width: 0%; } /* Hide on mobile */
        .col-email { width: 0%; } /* Hide on mobile */
        .col-role { width: 18%; }
        .col-status { width: 16%; }
        .col-date { width: 15%; }
        .col-actions { width: 15%; }

        .hide-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.4rem 0.2rem;
            font-size: 0.7rem;
        }

        .avatar-sm {
            width: 28px;
            height: 28px;
            font-size: 11px;
        }

        .btn-action {
            padding: 0.25rem 0.3rem;
            font-size: 0.6rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.15rem;
        }

        .badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
        }

        /* Bulk operations responsive */
        .row.g-3 .col-md-4 {
            margin-bottom: 0.75rem;
        }
    }

    /* Small Mobile (<576px) */
    @media (max-width: 575px) {
        .col-checkbox { width: 8%; }
        .col-id { width: 10%; }
        .col-username { width: 30%; }
        .col-fullname { width: 0%; } /* Hide */
        .col-email { width: 0%; } /* Hide */
        .col-role { width: 22%; }
        .col-status { width: 0%; } /* Hide */
        .col-date { width: 0%; } /* Hide */
        .col-actions { width: 30%; }

        .hide-small-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.3rem 0.15rem;
            font-size: 0.65rem;
        }

        .avatar-sm {
            width: 24px;
            height: 24px;
            font-size: 10px;
        }

        .btn-action {
            padding: 0.2rem 0.25rem;
            font-size: 0.55rem;
            margin: 0.05rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.1rem;
        }

        .badge {
            font-size: 0.55rem;
            padding: 0.15rem 0.3rem;
        }

        /* Page header responsive */
        .page-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .btn-primary {
            width: 100%;
            font-size: 0.8rem;
        }

        .card-header h5 {
            font-size: 0.85rem;
        }
    }

    /* Loading Animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .bi-hourglass-split {
        animation: spin 1s linear infinite;
    }

    /* Checkbox Styling */
    .form-check-input:checked {
        background-color: #7928ca;
        border-color: #7928ca;
    }

    .form-check-input:focus {
        border-color: #7928ca;
        box-shadow: 0 0 0 0.25rem rgba(121, 40, 202, 0.25);
    }

    /* Content Protection and Visibility */
    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Z-index management */
    .dropdown-menu {
        z-index: 1055 !important;
    }

    .card {
        position: relative;
        z-index: 1;
    }

    .table-responsive {
        position: relative;
        z-index: 2;
    }

    /* Prevent content overlap */
    .card-body {
        overflow: visible;
    }

    .table-container {
        margin-top: 0;
        clear: both;
    }

    /* Button text visibility on small screens */
    @media (max-width: 991px) {
        .btn-sm .d-none.d-lg-inline {
            display: none !important;
        }

        .btn-sm {
            min-width: 40px;
            padding: 0.375rem 0.5rem;
        }
    }

    /* Ensure dropdown menus don't get cut off */
    .btn-group .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        left: auto;
        transform: none;
    }

    /* RTL dropdown positioning */
    [dir="rtl"] .dropdown-menu-end {
        right: auto;
        left: 0;
    }

    /* Table cell content protection */
    .table td {
        position: relative;
        overflow: visible;
    }

    /* Bulk operations spacing */
    .bulk-operations-card {
        margin-bottom: 1.5rem;
    }

    /* Page header spacing */
    .page-header {
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
    <div class="mb-3 mb-md-0">
        <h4 class="fw-semibold mb-1" style="color: var(--sneat-text-heading); font-size: 1.375rem;">
            إدارة المستخدمين
        </h4>
        <p class="mb-0" style="color: var(--sneat-text-muted); font-size: 0.875rem;">إدارة حسابات المستخدمين وصلاحياتهم</p>
    </div>
    <div class="d-flex flex-column flex-sm-row gap-3">
        <div class="d-flex gap-2">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text" style="background-color: var(--sneat-card-bg); border-color: var(--sneat-border);">
                    <i class="bi bi-search" style="color: var(--sneat-text-muted);"></i>
                </span>
                <input type="text" class="form-control" placeholder="البحث عن مستخدم..." style="border-right: none;">
            </div>
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>طباعة</a></li>
            </ul>
        </div>
        <a href="/admin/users/new" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إضافة مستخدم
        </a>
    </div>
</div>

{% if users %}
<!-- Search Filters Card -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">مرشحات البحث</h5>
    </div>
    <div class="card-body">
        <form method="post" action="/admin/users/bulk-action" id="bulkUserForm">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--sneat-text-heading);">المستخدم</label>
                    <input type="text" class="form-control" placeholder="البحث بالاسم...">
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--sneat-text-heading);">الدور</label>
                    <select class="form-select">
                        <option value="">اختر الدور</option>
                        <option value="admin">مدير النظام</option>
                        <option value="user">مستخدم عادي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--sneat-text-heading);">الحالة</label>
                    <select class="form-select">
                        <option value="">اختر الحالة</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--sneat-text-heading);">العمليات المجمعة</label>
                    <select class="form-select" name="action">
                        <option value="">اختر العملية</option>
                        <option value="activate">تفعيل</option>
                        <option value="deactivate">إلغاء تفعيل</option>
                        <option value="make_admin">ترقية إلى مدير</option>
                        <option value="make_user">تحويل إلى مستخدم</option>
                    </select>
                </div>
            </div>
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center gap-2">
                    <span class="badge badge-light-secondary">
                        <span id="selectedCount">0</span> محدد
                    </span>
                </div>
                <button type="submit" class="btn btn-primary" id="bulkActionBtn" disabled>
                    <i class="bi bi-check-circle me-1"></i>
                    تطبيق العملية
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card table-container">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">قائمة المستخدمين</h5>
        <div class="d-flex align-items-center gap-2">
            <span class="badge badge-light-secondary">
                إجمالي {{ users|length }} مستخدم
            </span>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="users-table-container">
            <table class="users-table">
                <thead>
                    <tr>
                        <th class="col-checkbox text-center">
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </div>
                        </th>
                        <th class="col-username">المستخدم</th>
                        <th class="col-email hide-mobile hide-small-mobile">البريد الإلكتروني</th>
                        <th class="col-role text-center">الدور</th>
                        <th class="col-status text-center hide-small-mobile">الحالة</th>
                        <th class="col-actions text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td class="col-checkbox text-center">
                            {% if user.id != current_user.id %}
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" name="user_ids" value="{{ user.id }}"
                                       class="form-check-input user-checkbox" form="bulkUserForm">
                            </div>
                            {% else %}
                            <div class="text-center">
                                <i class="bi bi-person-badge text-primary" title="حسابك الحالي"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td class="col-username">
                            <div class="user-info-container">
                                <div class="avatar avatar-sm me-3" style="background-color: var(--sneat-primary); color: white; width: 38px; height: 38px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <span style="font-weight: 600; font-size: 0.875rem;">{{ user.username[:2].upper() }}</span>
                                </div>
                                <div class="user-info-text">
                                    <div class="username fw-medium" style="color: var(--sneat-text-heading); font-size: 0.875rem;" title="{{ user.username }}">{{ user.username }}</div>
                                    <div class="user-email" style="color: var(--sneat-text-muted); font-size: 0.8125rem;" title="{{ user.email }}">{{ user.email }}</div>
                                    {% if user.id == current_user.id %}
                                    <span class="badge badge-primary" style="font-size: 0.6875rem; padding: 0.125rem 0.375rem;">أنت</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="col-email hide-mobile hide-small-mobile">
                            <div class="email-text" style="color: var(--sneat-text-heading); font-size: 0.875rem;" title="{{ user.email }}">{{ user.email }}</div>
                        </td>
                        <td class="col-role text-center">
                            {% if user.role.value == 'admin' %}
                            <span class="badge badge-warning">
                                <i class="bi bi-crown-fill me-1"></i>
                                مدير
                            </span>
                            {% else %}
                            <span class="badge badge-info">
                                <i class="bi bi-person-fill me-1"></i>
                                مستخدم
                            </span>
                            {% endif %}
                        </td>
                        <td class="col-status text-center hide-small-mobile">
                            {% if user.is_active %}
                            <span class="badge badge-success">نشط</span>
                            {% else %}
                            <span class="badge badge-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td class="col-actions text-center">
                            {% if user.id != current_user.id %}
                            <div class="dropdown">
                                <button type="button" class="btn btn-sm btn-icon btn-outline-secondary dropdown-toggle hide-arrow"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="/admin/users/{{ user.id }}/edit">
                                            <i class="bi bi-pencil me-2"></i>
                                            تعديل
                                        </a>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/toggle-status" class="d-inline w-100">
                                            {% if user.is_active %}
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                                <i class="bi bi-pause-circle me-2"></i>
                                                إيقاف
                                            </button>
                                            {% else %}
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle me-2"></i>
                                                تفعيل
                                            </button>
                                            {% endif %}
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    {% if user.role.value != 'user' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="user">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مستخدم عادي؟')">
                                                <i class="bi bi-person me-2"></i>
                                                مستخدم عادي
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                    {% if user.role.value != 'admin' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="admin">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مدير نظام؟')">
                                                <i class="bi bi-crown me-2"></i>
                                                مدير النظام
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                            {% else %}
                            <span class="badge badge-primary">أنت</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% else %}
<!-- Empty State -->
<div class="card">
    <div class="card-body text-center" style="padding: 3rem 1.5rem;">
        <div class="avatar avatar-lg mx-auto mb-4" style="background-color: var(--sneat-light); color: var(--sneat-text-muted);">
            <i class="bi bi-people"></i>
        </div>
        <h5 class="fw-semibold mb-2" style="color: var(--sneat-text-heading);">لا يوجد مستخدمون</h5>
        <p class="mb-4" style="color: var(--sneat-text-muted);">لم يتم العثور على أي مستخدمين في النظام</p>
        <a href="/admin/users/new" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إنشاء أول مستخدم
        </a>
    </div>
</div>
{% endif %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const selectedCountTextSpan = document.getElementById('selectedCountText');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const bulkForm = document.getElementById('bulkUserForm');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }

    // Individual checkbox change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // Update select all checkbox state
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                const totalCheckboxes = userCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCheckboxes;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCheckboxes;
            }
        });
    });

    // Update selected count and button state
    function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;

        // Update count displays
        if (selectedCountSpan) selectedCountSpan.textContent = checkedCount;
        if (selectedCountTextSpan) selectedCountTextSpan.textContent = checkedCount;

        // Update button state
        if (bulkActionBtn) {
            bulkActionBtn.disabled = checkedCount === 0;

            // Update button text based on selection
            if (checkedCount === 0) {
                bulkActionBtn.innerHTML = '<i class="bi bi-play-circle me-2"></i>تطبيق على المحدد';
            } else {
                bulkActionBtn.innerHTML = `<i class="bi bi-play-circle me-2"></i>تطبيق على ${checkedCount} مستخدم`;
            }
        }
    }

    // Form submission confirmation
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            const actionSelect = this.querySelector('select[name="action"]');
            const action = actionSelect.value;

            if (checkedCount === 0) {
                e.preventDefault();
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('يرجى اختيار العملية المطلوبة');
                actionSelect.focus();
                return;
            }

            const actionNames = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'make_admin': 'ترقية إلى مدير نظام',
                'make_user': 'تحويل إلى مستخدم عادي'
            };

            const actionName = actionNames[action] || action;
            const confirmMessage = `هل أنت متأكد من ${actionName} ${checkedCount} مستخدم؟\n\nهذا الإجراء سيؤثر على المستخدمين المحددين.`;

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            bulkActionBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري التطبيق...';
            bulkActionBtn.disabled = true;

            // Disable form elements during submission
            actionSelect.disabled = true;
            userCheckboxes.forEach(checkbox => checkbox.disabled = true);
        });
    }

    // Add hover effects to action buttons
    const actionButtons = document.querySelectorAll('.btn-sm');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Initialize
    updateSelectedCount();

    // Add smooth transitions
    const style = document.createElement('style');
    style.textContent = `
        .btn-sm {
            transition: all 0.2s ease;
        }
        .form-check-input {
            transition: all 0.2s ease;
        }
        .table tbody tr {
            transition: background-color 0.2s ease;
        }
        .avatar-sm {
            transition: transform 0.2s ease;
        }
        .table tbody tr:hover .avatar-sm {
            transform: scale(1.05);
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}

{% endblock %}
