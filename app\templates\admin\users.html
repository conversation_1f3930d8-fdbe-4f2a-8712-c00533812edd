{% extends "base.html" %}

{% block title %}إدارة المستخدمين - CMSVS{% endblock %}

{% block extra_css %}
<!-- Force browser cache refresh -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<style>
    /* High Specificity CSS Reset for Users Page */
    html body .users-page-container * {
        box-sizing: border-box;
    }

    /* Force override of base.html styles with maximum specificity */
    html body .users-page-container .card,
    html body .users-page-container .card.card,
    html body .users-page-container div.card {
        background: var(--template-white) !important;
        border: none !important;
        border-radius: var(--template-radius) !important;
        box-shadow: var(--template-shadow) !important;
        transform: none !important;
        animation: none !important;
        margin-bottom: 1.5rem !important;
        overflow: hidden !important;
        position: relative !important;
        transition: all 0.3s ease !important;
    }

    html body .users-page-container .card:hover,
    html body .users-page-container .card.card:hover,
    html body .users-page-container div.card:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--template-shadow-hover) !important;
    }

    html body .users-page-container .btn,
    html body .users-page-container .btn.btn,
    html body .users-page-container button.btn {
        border-radius: 12px !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        padding: 0.5rem 1.25rem !important;
        transition: all 0.3s ease !important;
        border: 1px solid transparent !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
        text-transform: none !important;
    }

    html body .users-page-container .btn-primary,
    html body .users-page-container .btn.btn-primary,
    html body .users-page-container button.btn-primary {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        border: none !important;
    }

    html body .users-page-container .btn-primary:hover,
    html body .users-page-container .btn.btn-primary:hover,
    html body .users-page-container button.btn-primary:hover {
        background: linear-gradient(135deg, #3d43a8 0%, #7a80db 100%) !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    /* Custom Template Design - Users Page */
    :root {
        /* Template Color Scheme */
        --template-primary-start: #4e54c8;
        --template-primary-end: #8f94fb;
        --template-secondary-start: #6a82fb;
        --template-secondary-end: #fc5c7d;
        --template-background: #f8f9fa;
        --template-white: #ffffff;
        --template-dark: #333333;
        --template-muted: #6c757d;
        --template-light-gray: #e9ecef;
        --template-success: #28a745;
        --template-warning: #ffc107;
        --template-danger: #dc3545;
        --template-info: #17a2b8;

        /* Template Shadows */
        --template-shadow: 0 6px 15px rgba(0,0,0,0.08);
        --template-shadow-hover: 0 10px 25px rgba(0,0,0,0.12);

        /* Template Border Radius */
        --template-radius: 15px;
        --template-radius-small: 12px;
        --template-radius-large: 20px;
    }

    /* Page Layout - Override base.html styles */
    body {
        background-color: var(--template-background) !important;
        background: var(--template-background) !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        color: var(--template-dark) !important;
    }

    .container-xxl {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    /* Template Gradient Header with Maximum Specificity */
    html body .users-page-container .gradient-header,
    html body .gradient-header {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        padding: 3rem 0 !important;
        margin-bottom: 2rem !important;
        border-radius: 0 0 20px 20px !important;
        box-shadow: var(--template-shadow) !important;
        text-align: center !important;
        position: relative !important;
        z-index: 1 !important;
    }

    html body .users-page-container .gradient-header .display-5,
    html body .gradient-header .display-5 {
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        margin-bottom: 0.5rem !important;
        color: white !important;
    }

    html body .users-page-container .gradient-header .lead,
    html body .gradient-header .lead {
        font-size: 1.125rem !important;
        opacity: 0.9 !important;
        margin-bottom: 0 !important;
        color: white !important;
    }

    html body .users-page-container .gradient-header i,
    html body .gradient-header i {
        font-size: 3rem !important;
        margin-bottom: 1rem !important;
        opacity: 0.8 !important;
        color: white !important;
    }

    /* Template Avatar Styles */
    .avatar {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%);
        color: white;
        font-weight: bold;
        flex-shrink: 0;
        transition: all 0.3s ease;
        margin-left: 10px;
    }

    .avatar:hover {
        transform: scale(1.05);
    }

    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }

    .avatar-xs {
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    /* Template Card Styles - Apply to users page container */
    .users-page-container .card {
        background: var(--template-white) !important;
        border: none !important;
        border-radius: var(--template-radius) !important;
        box-shadow: var(--template-shadow) !important;
        transition: all 0.3s ease !important;
        margin-bottom: 1.5rem !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .users-page-container .card:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--template-shadow-hover) !important;
    }

    html body .users-page-container .card-header,
    html body .users-page-container .card .card-header,
    html body .users-page-container div.card .card-header {
        background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%) !important;
        border-bottom: none !important;
        border-radius: var(--template-radius) var(--template-radius) 0 0 !important;
        padding: 1.5rem 2rem !important;
        margin-bottom: 0 !important;
        position: relative !important;
        color: white !important;
    }

    .card-body {
        padding: 1.5rem 2rem;
    }

    .card-title {
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: white;
    }

    .card-subtitle {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }

    /* Template Button Styles - Apply to users page container */
    .users-page-container .btn {
        border-radius: var(--template-radius-small) !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        padding: 0.5rem 1.25rem !important;
        transition: all 0.3s ease !important;
        border: 1px solid transparent !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
        text-transform: none !important;
    }

    .users-page-container .btn:hover {
        transform: translateY(-2px) !important;
    }

    .users-page-container .btn-primary {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        border: none !important;
    }

    .users-page-container .btn-primary:hover {
        background: linear-gradient(135deg, #3d43a8 0%, #7a80db 100%) !important;
        color: white !important;
    }

    .btn-outline-primary {
        color: var(--template-primary-start);
        border: 1px solid var(--template-primary-start);
        background: transparent;
    }

    .btn-outline-primary:hover {
        background: var(--template-primary-start);
        border-color: var(--template-primary-start);
        color: white;
    }

    .btn-outline-secondary {
        color: var(--template-muted);
        border: 1px solid #dee2e6;
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background: var(--template-muted);
        border-color: var(--template-muted);
        color: white;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
        border-radius: var(--template-radius-small);
    }

    .btn-icon {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--template-radius-small);
    }

    /* Custom Action Buttons */
    .btn-edit {
        background-color: var(--template-primary-start);
        color: white;
        border: none;
    }

    .btn-edit:hover {
        background-color: #3d43a8;
        color: white;
    }

    .btn-delete {
        background-color: var(--template-secondary-end);
        color: white;
        border: none;
    }

    .btn-delete:hover {
        background-color: #e04a6b;
        color: white;
    }

    .hide-arrow::after {
        display: none !important;
    }

    /* Template Form Controls */
    .form-control, .form-select {
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        color: var(--template-dark);
        background-color: var(--template-white);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--template-primary-start);
        box-shadow: 0 0 0 0.2rem rgba(78, 84, 200, 0.25);
        outline: 0;
    }

    .form-label {
        font-weight: 500;
        color: var(--template-dark);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .input-group-text {
        background: var(--template-white);
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        color: var(--template-muted);
        font-weight: 400;
    }

    /* Template Dropdown Styles */
    .dropdown-menu {
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius);
        box-shadow: var(--template-shadow);
        padding: 0.5rem;
        margin-top: 0.25rem;
        min-width: 180px;
        background: var(--template-white);
    }

    .dropdown-item {
        border-radius: var(--template-radius-small);
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        color: var(--template-dark);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
        font-weight: 400;
        margin-bottom: 0.125rem;
    }

    .dropdown-item:hover {
        background-color: rgba(78, 84, 200, 0.1);
        color: var(--template-primary-start);
    }

    .dropdown-item i {
        width: 1rem;
        text-align: center;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover i {
        color: var(--template-primary-start);
    }

    .dropdown-divider {
        margin: 0.5rem 0;
        border-color: #dee2e6;
        opacity: 1;
    }

    /* Template Badge Styles */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: var(--template-radius-small);
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        white-space: nowrap;
        text-transform: none;
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: translateY(-1px);
    }

    .badge-primary {
        background-color: rgba(78, 84, 200, 0.1);
        color: var(--template-primary-start);
        border: 1px solid rgba(78, 84, 200, 0.2);
    }

    .badge-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--template-success);
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .badge-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--template-warning);
        border: 1px solid rgba(255, 193, 7, 0.2);
    }

    .badge-secondary {
        background-color: rgba(108, 117, 125, 0.1);
        color: var(--template-muted);
        border: 1px solid rgba(108, 117, 125, 0.2);
    }

    .badge-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--template-info);
        border: 1px solid rgba(23, 162, 184, 0.2);
    }

    .badge-light-secondary {
        background-color: rgba(108, 117, 125, 0.08);
        color: var(--template-muted);
        border: 1px solid rgba(108, 117, 125, 0.15);
    }

    /* Template Table Styles */
    .users-table-container {
        width: 100%;
        overflow: hidden;
        background: var(--template-white);
        border-radius: var(--template-radius);
        box-shadow: var(--template-shadow);
    }

    .users-table {
        width: 100%;
        table-layout: fixed;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    html body .users-page-container .users-table th,
    html body .users-page-container table.users-table th,
    html body .users-page-container .table thead th,
    html body .users-page-container table.table thead th {
        background-color: var(--template-light-gray) !important;
        background: var(--template-light-gray) !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding: 1rem 0.75rem !important;
        font-weight: 600 !important;
        color: var(--template-dark) !important;
        font-size: 0.8125rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        vertical-align: middle !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        position: relative !important;
        border: none !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
    }

    .users-table th:first-child {
        padding-left: 1.5rem;
        border-radius: var(--template-radius) 0 0 0;
    }

    .users-table th:last-child {
        padding-right: 1.5rem;
        border-radius: 0 var(--template-radius) 0 0;
    }

    .users-table td {
        border-bottom: 1px solid #f8f9fa;
        padding: 1rem 0.75rem;
        vertical-align: middle;
        color: var(--template-dark);
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .users-table td:first-child {
        padding-left: 1.5rem;
    }

    .users-table td:last-child {
        padding-right: 1.5rem;
    }

    .users-table tbody tr {
        transition: all 0.3s ease;
        background: var(--template-white);
    }

    html body .users-page-container .users-table tbody tr:hover,
    html body .users-page-container table.users-table tbody tr:hover,
    html body .users-page-container .table tbody tr:hover,
    html body .users-page-container table.table tbody tr:hover {
        background-color: rgba(78, 84, 200, 0.05) !important;
        background: rgba(78, 84, 200, 0.05) !important;
        transform: none !important;
    }

    .users-table tbody tr:last-child td:first-child {
        border-radius: 0 0 0 var(--template-radius);
    }

    .users-table tbody tr:last-child td:last-child {
        border-radius: 0 0 var(--template-radius) 0;
    }

    /* Responsive Column Widths */
    .col-checkbox { width: 4%; }
    .col-id { width: 6%; }
    .col-username { width: 15%; }
    .col-fullname { width: 18%; }
    .col-email { width: 20%; }
    .col-role { width: 10%; }
    .col-status { width: 9%; }
    .col-date { width: 10%; }
    .col-actions { width: 8%; }

    /* Template User Info Container */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 0;
        flex: 1;
    }

    .user-info-text {
        min-width: 0;
        flex: 1;
    }

    .user-info-text .username {
        font-weight: 600;
        color: var(--template-dark);
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 0.125rem;
        transition: color 0.3s ease;
    }

    .user-info-text .user-email {
        font-weight: 400;
        color: var(--template-muted);
        font-size: 0.8125rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .user-info-text .current-user-badge {
        font-size: 0.6875rem;
        color: var(--template-primary-start);
        font-weight: 500;
    }

    /* Template Action Buttons */
    .action-buttons-container {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
    }

    .btn-action {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        border-radius: var(--template-radius-small);
        min-width: auto;
        white-space: nowrap;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-action i {
        font-size: 0.875rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
    }

    /* Enhanced Soft UI Dropdown Styles */
    .dropdown-menu {
        border: none;
        border-radius: var(--soft-border-radius-lg);
        box-shadow: var(--soft-shadow-lg);
        z-index: 1050;
        min-width: 200px;
        padding: 0.75rem;
        background: var(--soft-white);
        backdrop-filter: blur(10px);
    }

    .dropdown-item {
        border-radius: var(--soft-border-radius);
        margin: 0.25rem 0;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        color: var(--soft-text-heading);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
        border: 1px solid transparent;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, var(--soft-primary-light) 0%, rgba(94, 114, 228, 0.08) 100%);
        color: var(--soft-primary);
        transform: translateX(-3px);
        border-color: rgba(94, 114, 228, 0.2);
        box-shadow: 0 0.125rem 0.25rem rgba(94, 114, 228, 0.15);
    }

    .dropdown-item i {
        width: 1.25rem;
        text-align: center;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover i {
        color: var(--soft-primary);
        transform: scale(1.1);
    }

    /* Content Styling */
    .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }

    /* User Info Styles */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .avatar {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .avatar-sm {
        width: 38px;
        height: 38px;
        font-size: 0.875rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .user-info-text {
        flex: 1;
        min-width: 0;
    }

    .username {
        font-weight: 600;
        color: var(--template-dark);
        font-size: 0.875rem;
        line-height: 1.2;
        margin-bottom: 0.125rem;
        transition: color 0.3s ease;
    }

    .user-email {
        color: var(--template-muted);
        font-size: 0.8125rem;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 400;
    }

    .current-user-badge {
        font-size: 0.6875rem;
        color: var(--template-primary-start);
        font-weight: 500;
        margin-top: 0.125rem;
    }

    .fullname-text, .email-text {
        color: var(--template-dark);
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .email-text {
        font-size: 0.8125rem;
        color: var(--template-muted);
        font-weight: 400;
    }

    .email-text:hover {
        color: var(--template-primary-start);
    }

    .fullname-text {
        font-size: 0.875rem;
        color: var(--template-dark);
        font-weight: 500;
    }

    /* Template Form Check Styles */
    .form-check-input {
        width: 1.125rem;
        height: 1.125rem;
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        background-color: var(--template-white);
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: var(--template-primary-start);
        border-color: var(--template-primary-start);
    }

    .form-check-input:focus {
        border-color: var(--template-primary-start);
        box-shadow: 0 0 0 0.2rem rgba(78, 84, 200, 0.25);
    }

    .form-check-input:hover {
        border-color: var(--template-primary-start);
    }

    /* Responsive Design - No Horizontal Scroll */

    /* Large Desktop (1200px+) */
    @media (min-width: 1200px) {
        .col-checkbox { width: 4%; }
        .col-id { width: 6%; }
        .col-username { width: 15%; }
        .col-fullname { width: 18%; }
        .col-email { width: 20%; }
        .col-role { width: 10%; }
        .col-status { width: 9%; }
        .col-date { width: 10%; }
        .col-actions { width: 8%; }

        .btn-action .btn-text { display: inline; }
    }

    /* Desktop (992px - 1199px) */
    @media (max-width: 1199px) and (min-width: 992px) {
        .col-checkbox { width: 4%; }
        .col-id { width: 6%; }
        .col-username { width: 16%; }
        .col-fullname { width: 18%; }
        .col-email { width: 22%; }
        .col-role { width: 10%; }
        .col-status { width: 9%; }
        .col-date { width: 8%; }
        .col-actions { width: 7%; }

        .users-table th,
        .users-table td {
            padding: 0.6rem 0.4rem;
            font-size: 0.8rem;
        }

        .btn-action .btn-text { display: none; }
        .btn-action { padding: 0.35rem 0.5rem; }
    }

    /* Tablet (768px - 991px) */
    @media (max-width: 991px) and (min-width: 768px) {
        .col-checkbox { width: 5%; }
        .col-id { width: 7%; }
        .col-username { width: 18%; }
        .col-fullname { width: 0%; } /* Hide on tablet */
        .col-email { width: 25%; }
        .col-role { width: 12%; }
        .col-status { width: 11%; }
        .col-date { width: 12%; }
        .col-actions { width: 10%; }

        .hide-tablet { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.5rem 0.3rem;
            font-size: 0.75rem;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }

        .btn-action {
            padding: 0.3rem 0.4rem;
            font-size: 0.65rem;
        }

        .btn-action .btn-text { display: none; }

        .badge {
            font-size: 0.65rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* Mobile (576px - 767px) */
    @media (max-width: 767px) and (min-width: 576px) {
        .col-checkbox { width: 6%; }
        .col-id { width: 8%; }
        .col-username { width: 22%; }
        .col-fullname { width: 0%; } /* Hide on mobile */
        .col-email { width: 0%; } /* Hide on mobile */
        .col-role { width: 18%; }
        .col-status { width: 16%; }
        .col-date { width: 15%; }
        .col-actions { width: 15%; }

        .hide-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.4rem 0.2rem;
            font-size: 0.7rem;
        }

        .avatar-sm {
            width: 28px;
            height: 28px;
            font-size: 11px;
        }

        .btn-action {
            padding: 0.25rem 0.3rem;
            font-size: 0.6rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.15rem;
        }

        .badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
        }

        /* Bulk operations responsive */
        .row.g-3 .col-md-4 {
            margin-bottom: 0.75rem;
        }
    }

    /* Small Mobile (<576px) */
    @media (max-width: 575px) {
        .col-checkbox { width: 8%; }
        .col-id { width: 10%; }
        .col-username { width: 30%; }
        .col-fullname { width: 0%; } /* Hide */
        .col-email { width: 0%; } /* Hide */
        .col-role { width: 22%; }
        .col-status { width: 0%; } /* Hide */
        .col-date { width: 0%; } /* Hide */
        .col-actions { width: 30%; }

        .hide-small-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.3rem 0.15rem;
            font-size: 0.65rem;
        }

        .avatar-sm {
            width: 24px;
            height: 24px;
            font-size: 10px;
        }

        .btn-action {
            padding: 0.2rem 0.25rem;
            font-size: 0.55rem;
            margin: 0.05rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.1rem;
        }

        .badge {
            font-size: 0.55rem;
            padding: 0.15rem 0.3rem;
        }

        /* Page header responsive */
        .page-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .btn-primary {
            width: 100%;
            font-size: 0.8rem;
        }

        .card-header h5 {
            font-size: 0.85rem;
        }
    }

    /* Loading Animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .bi-hourglass-split {
        animation: spin 1s linear infinite;
    }

    /* Checkbox Styling */
    .form-check-input:checked {
        background-color: #7928ca;
        border-color: #7928ca;
    }

    .form-check-input:focus {
        border-color: #7928ca;
        box-shadow: 0 0 0 0.25rem rgba(121, 40, 202, 0.25);
    }

    /* Content Protection and Visibility */
    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Z-index management */
    .dropdown-menu {
        z-index: 1055 !important;
    }

    .card {
        position: relative;
        z-index: 1;
    }

    .table-responsive {
        position: relative;
        z-index: 2;
    }

    /* Prevent content overlap */
    .card-body {
        overflow: visible;
    }

    .table-container {
        margin-top: 0;
        clear: both;
    }

    /* Button text visibility on small screens */
    @media (max-width: 991px) {
        .btn-sm .d-none.d-lg-inline {
            display: none !important;
        }

        .btn-sm {
            min-width: 40px;
            padding: 0.375rem 0.5rem;
        }
    }

    /* Ensure dropdown menus don't get cut off */
    .btn-group .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        left: auto;
        transform: none;
    }

    /* RTL dropdown positioning */
    [dir="rtl"] .dropdown-menu-end {
        right: auto;
        left: 0;
    }

    /* Table cell content protection */
    .table td {
        position: relative;
        overflow: visible;
    }

    /* Bulk operations spacing */
    .bulk-operations-card {
        margin-bottom: 1.5rem;
    }

    /* Page header spacing */
    .page-header {
        margin-bottom: 2rem;
    }

    /* Template Animations and Enhancements */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Enhanced hover effects */
    .users-table tbody tr:hover .avatar {
        transform: scale(1.05);
    }

    .users-table tbody tr:hover .username {
        color: var(--template-primary-start);
    }

    /* Loading state for bulk actions */
    .btn[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    /* Enhanced search input styling */
    .input-group .form-control:focus + .input-group-text,
    .input-group .form-control:focus ~ .input-group-text {
        border-color: var(--template-primary-start);
        background-color: rgba(78, 84, 200, 0.1);
    }

    /* RTL specific enhancements */
    [dir="rtl"] .dropdown-item:hover {
        transform: translateX(3px);
    }

    /* Content styling */
    .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }

    .truncate-text:hover {
        color: var(--template-primary-start);
    }
</style>
{% endblock %}

{% block content %}
<div class="users-page-container">
<!-- Template Gradient Header -->
<div class="gradient-header">
    <div class="container-xxl">
        <i class="bi bi-people-fill d-block"></i>
        <h1 class="display-5 mb-3">إدارة المستخدمين</h1>
        <p class="lead">إدارة حسابات المستخدمين وصلاحياتهم في النظام</p>
    </div>
</div>

<div class="container-xxl flex-grow-1 container-p-y">
<!-- Action Header -->
<div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
    <div class="mb-3 mb-md-0">
        <h4 class="fw-semibold mb-1" style="color: var(--template-dark); font-size: 1.375rem;">
            قائمة المستخدمين
        </h4>
        <p class="mb-0" style="color: var(--template-muted); font-size: 0.875rem;">إدارة وتحرير بيانات المستخدمين</p>
    </div>
    <div class="d-flex flex-column flex-sm-row gap-3">
        <div class="d-flex gap-2">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text" style="background-color: var(--template-white); border-color: #dee2e6;">
                    <i class="bi bi-search" style="color: var(--template-muted);"></i>
                </span>
                <input type="text" class="form-control" placeholder="البحث عن مستخدم..." style="border-right: none;">
            </div>
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>طباعة</a></li>
            </ul>
        </div>
        <a href="/admin/users/new" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إضافة مستخدم
        </a>
    </div>
</div>

{% if users %}
<!-- Search Filters Card -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">مرشحات البحث</h5>
    </div>
    <div class="card-body">
        <form method="post" action="/admin/users/bulk-action" id="bulkUserForm">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--template-dark);">المستخدم</label>
                    <input type="text" class="form-control" placeholder="البحث بالاسم...">
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--template-dark);">الدور</label>
                    <select class="form-select">
                        <option value="">اختر الدور</option>
                        <option value="admin">مدير النظام</option>
                        <option value="user">مستخدم عادي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--template-dark);">الحالة</label>
                    <select class="form-select">
                        <option value="">اختر الحالة</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-medium" style="color: var(--template-dark);">العمليات المجمعة</label>
                    <select class="form-select" name="action">
                        <option value="">اختر العملية</option>
                        <option value="activate">تفعيل</option>
                        <option value="deactivate">إلغاء تفعيل</option>
                        <option value="make_admin">ترقية إلى مدير</option>
                        <option value="make_user">تحويل إلى مستخدم</option>
                    </select>
                </div>
            </div>
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center gap-2">
                    <span class="badge badge-light-secondary">
                        <span id="selectedCount">0</span> محدد
                    </span>
                </div>
                <button type="submit" class="btn btn-primary" id="bulkActionBtn" disabled>
                    <i class="bi bi-check-circle me-1"></i>
                    تطبيق العملية
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card table-container">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">قائمة المستخدمين</h5>
        <div class="d-flex align-items-center gap-2">
            <span class="badge badge-light-secondary">
                إجمالي {{ users|length }} مستخدم
            </span>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="users-table-container">
            <table class="users-table">
                <thead>
                    <tr>
                        <th class="col-checkbox text-center">
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </div>
                        </th>
                        <th class="col-username">المستخدم</th>
                        <th class="col-email hide-mobile hide-small-mobile">البريد الإلكتروني</th>
                        <th class="col-role text-center">الدور</th>
                        <th class="col-status text-center hide-small-mobile">الحالة</th>
                        <th class="col-actions text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td class="col-checkbox text-center">
                            {% if user.id != current_user.id %}
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" name="user_ids" value="{{ user.id }}"
                                       class="form-check-input user-checkbox" form="bulkUserForm">
                            </div>
                            {% else %}
                            <div class="text-center">
                                <i class="bi bi-person-badge text-primary" title="حسابك الحالي"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td class="col-username">
                            <div class="user-info-container">
                                <div class="avatar avatar-sm me-3" style="background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%); color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 10px;">
                                    <span style="font-weight: bold; font-size: 0.875rem;">{{ user.username[:2].upper() }}</span>
                                </div>
                                <div class="user-info-text">
                                    <div class="username fw-medium" style="color: var(--template-dark); font-size: 0.875rem; font-weight: 600;" title="{{ user.username }}">{{ user.username }}</div>
                                    <div class="user-email" style="color: var(--template-muted); font-size: 0.8125rem; font-weight: 400;" title="{{ user.email }}">{{ user.email }}</div>
                                    {% if user.id == current_user.id %}
                                    <span class="badge badge-primary" style="font-size: 0.6875rem; padding: 0.125rem 0.375rem;">أنت</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="col-email hide-mobile hide-small-mobile">
                            <div class="email-text" style="color: var(--template-dark); font-size: 0.875rem; font-weight: 500;" title="{{ user.email }}">{{ user.email }}</div>
                        </td>
                        <td class="col-role text-center">
                            {% if user.role.value == 'admin' %}
                            <span class="badge badge-warning">
                                <i class="bi bi-crown-fill me-1"></i>
                                مدير
                            </span>
                            {% else %}
                            <span class="badge badge-info">
                                <i class="bi bi-person-fill me-1"></i>
                                مستخدم
                            </span>
                            {% endif %}
                        </td>
                        <td class="col-status text-center hide-small-mobile">
                            {% if user.is_active %}
                            <span class="badge badge-success">نشط</span>
                            {% else %}
                            <span class="badge badge-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td class="col-actions text-center">
                            {% if user.id != current_user.id %}
                            <div class="dropdown">
                                <button type="button" class="btn btn-sm btn-icon btn-outline-secondary dropdown-toggle hide-arrow"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item btn-edit" href="/admin/users/{{ user.id }}/edit">
                                            <i class="bi bi-pencil me-2"></i>
                                            تعديل
                                        </a>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/toggle-status" class="d-inline w-100">
                                            {% if user.is_active %}
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                                <i class="bi bi-pause-circle me-2"></i>
                                                إيقاف
                                            </button>
                                            {% else %}
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle me-2"></i>
                                                تفعيل
                                            </button>
                                            {% endif %}
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    {% if user.role.value != 'user' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="user">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مستخدم عادي؟')">
                                                <i class="bi bi-person me-2"></i>
                                                مستخدم عادي
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                    {% if user.role.value != 'admin' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="admin">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مدير نظام؟')">
                                                <i class="bi bi-crown me-2"></i>
                                                مدير النظام
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                            {% else %}
                            <span class="badge badge-primary">أنت</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% else %}
<!-- Empty State -->
<div class="card">
    <div class="card-body text-center" style="padding: 3rem 1.5rem;">
        <div class="avatar avatar-lg mx-auto mb-4" style="background: linear-gradient(135deg, var(--template-light-gray) 0%, #f8f9fa 100%); color: var(--template-muted); box-shadow: var(--template-shadow);">
            <i class="bi bi-people"></i>
        </div>
        <h5 class="fw-semibold mb-2" style="color: var(--template-dark);">لا يوجد مستخدمون</h5>
        <p class="mb-4" style="color: var(--template-muted);">لم يتم العثور على أي مستخدمين في النظام</p>
        <a href="/admin/users/new" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إنشاء أول مستخدم
        </a>
    </div>
</div>
{% endif %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const selectedCountTextSpan = document.getElementById('selectedCountText');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const bulkForm = document.getElementById('bulkUserForm');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }

    // Individual checkbox change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // Update select all checkbox state
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                const totalCheckboxes = userCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCheckboxes;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCheckboxes;
            }
        });
    });

    // Update selected count and button state
    function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;

        // Update count displays
        if (selectedCountSpan) selectedCountSpan.textContent = checkedCount;
        if (selectedCountTextSpan) selectedCountTextSpan.textContent = checkedCount;

        // Update button state
        if (bulkActionBtn) {
            bulkActionBtn.disabled = checkedCount === 0;

            // Update button text based on selection
            if (checkedCount === 0) {
                bulkActionBtn.innerHTML = '<i class="bi bi-play-circle me-2"></i>تطبيق على المحدد';
            } else {
                bulkActionBtn.innerHTML = `<i class="bi bi-play-circle me-2"></i>تطبيق على ${checkedCount} مستخدم`;
            }
        }
    }

    // Form submission confirmation
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            const actionSelect = this.querySelector('select[name="action"]');
            const action = actionSelect.value;

            if (checkedCount === 0) {
                e.preventDefault();
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('يرجى اختيار العملية المطلوبة');
                actionSelect.focus();
                return;
            }

            const actionNames = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'make_admin': 'ترقية إلى مدير نظام',
                'make_user': 'تحويل إلى مستخدم عادي'
            };

            const actionName = actionNames[action] || action;
            const confirmMessage = `هل أنت متأكد من ${actionName} ${checkedCount} مستخدم؟\n\nهذا الإجراء سيؤثر على المستخدمين المحددين.`;

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            bulkActionBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري التطبيق...';
            bulkActionBtn.disabled = true;

            // Disable form elements during submission
            actionSelect.disabled = true;
            userCheckboxes.forEach(checkbox => checkbox.disabled = true);
        });
    }

    // Add hover effects to action buttons
    const actionButtons = document.querySelectorAll('.btn-sm');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Initialize
    updateSelectedCount();

    // Add smooth transitions
    const style = document.createElement('style');
    style.textContent = `
        .btn-sm {
            transition: all 0.2s ease;
        }
        .form-check-input {
            transition: all 0.2s ease;
        }
        .table tbody tr {
            transition: background-color 0.2s ease;
        }
        .avatar-sm {
            transition: transform 0.2s ease;
        }
        .table tbody tr:hover .avatar-sm {
            transform: scale(1.05);
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}

</div> <!-- Close container-xxl -->
</div> <!-- Close users-page-container -->

<script>
// Force apply template styles after page load
document.addEventListener('DOMContentLoaded', function() {
    // Force gradient header styles
    const gradientHeader = document.querySelector('.gradient-header');
    if (gradientHeader) {
        gradientHeader.style.cssText = `
            background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%) !important;
            color: white !important;
            padding: 3rem 0 !important;
            margin-bottom: 2rem !important;
            border-radius: 0 0 20px 20px !important;
            box-shadow: 0 6px 15px rgba(0,0,0,0.08) !important;
            text-align: center !important;
        `;
    }

    // Force card styles
    const cards = document.querySelectorAll('.users-page-container .card');
    cards.forEach(card => {
        card.style.cssText = `
            background: #ffffff !important;
            border: none !important;
            border-radius: 15px !important;
            box-shadow: 0 6px 15px rgba(0,0,0,0.08) !important;
            margin-bottom: 1.5rem !important;
            overflow: hidden !important;
            transition: all 0.3s ease !important;
        `;

        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.12)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 6px 15px rgba(0,0,0,0.08)';
        });
    });

    // Force card header styles
    const cardHeaders = document.querySelectorAll('.users-page-container .card-header');
    cardHeaders.forEach(header => {
        header.style.cssText = `
            background: linear-gradient(135deg, #6a82fb 0%, #fc5c7d 100%) !important;
            border-bottom: none !important;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem 2rem !important;
            color: white !important;
        `;
    });

    // Force button styles
    const primaryButtons = document.querySelectorAll('.users-page-container .btn-primary');
    primaryButtons.forEach(btn => {
        btn.style.cssText = `
            background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%) !important;
            border: none !important;
            border-radius: 12px !important;
            color: white !important;
            padding: 0.5rem 1.25rem !important;
            transition: all 0.3s ease !important;
        `;

        btn.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #3d43a8 0%, #7a80db 100%)';
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.background = 'linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%)';
            this.style.transform = 'translateY(0)';
        });
    });

    // Force table header styles
    const tableHeaders = document.querySelectorAll('.users-page-container .users-table th, .users-page-container .table thead th');
    tableHeaders.forEach(th => {
        th.style.cssText = `
            background-color: #e9ecef !important;
            background: #e9ecef !important;
            border-bottom: 1px solid #dee2e6 !important;
            font-weight: 600 !important;
            color: #333333 !important;
            text-transform: uppercase !important;
            border-top: none !important;
            border-left: none !important;
            border-right: none !important;
        `;
    });

    // Force table row hover styles
    const tableRows = document.querySelectorAll('.users-page-container .users-table tbody tr, .users-page-container .table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(78, 84, 200, 0.05) !important';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>

{% endblock %}
