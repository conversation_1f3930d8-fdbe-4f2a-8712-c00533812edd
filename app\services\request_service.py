from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, extract
from app.models.request import Request, RequestStatus
from app.models.file import File
from app.models.user import User, UserRole
from app.utils.file_handler import <PERSON><PERSON><PERSON><PERSON>
from fastapi import UploadFile, HTTPException
from datetime import datetime, timedelta


class RequestService:
    """Service for request operations"""
    
    @staticmethod
    def create_request(
        db: Session,
        user_id: int,
        request_name: str,
        request_title: str,
        description: Optional[str] = None
    ) -> Request:
        """Create a new request"""
        request = Request(
            request_name=request_name,
            request_title=request_title,
            request_number=Request.generate_request_number(),
            unique_code=Request.generate_unique_code(),
            description=description,
            user_id=user_id
        )
        
        db.add(request)
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    def get_request_by_id(db: Session, request_id: int) -> Optional[Request]:
        """Get request by ID"""
        return db.query(Request).filter(Request.id == request_id).first()
    
    @staticmethod
    def get_request_by_number(db: Session, request_number: str) -> Optional[Request]:
        """Get request by request number"""
        return db.query(Request).filter(Request.request_number == request_number).first()
    
    @staticmethod
    def get_user_requests(
        db: Session,
        user_id: int,
        skip: int = 0,
        limit: int = 50,
        include_archived: bool = False
    ) -> List[Request]:
        """Get requests for a specific user"""
        query = db.query(Request).filter(Request.user_id == user_id)

        # Only show non-archived requests by default
        if not include_archived:
            query = query.filter(Request.is_archived == False)

        return query.order_by(desc(Request.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_all_requests(
        db: Session, 
        skip: int = 0, 
        limit: int = 50,
        status: Optional[RequestStatus] = None
    ) -> List[Request]:
        """Get all requests with optional status filter"""
        query = db.query(Request)
        
        if status:
            query = query.filter(Request.status == status)
        
        # Only show non-archived requests by default
        query = query.filter(Request.is_archived == False)

        return query.order_by(desc(Request.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def update_request_status(
        db: Session, 
        request_id: int, 
        status: RequestStatus
    ) -> Optional[Request]:
        """Update request status"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return None
        
        request.status = status
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    def update_request(
        db: Session,
        request_id: int,
        request_name: Optional[str] = None,
        request_title: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[RequestStatus] = None
    ) -> Optional[Request]:
        """Update request information"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return None
        
        if request_name is not None:
            request.request_name = request_name
        
        if request_title is not None:
            request.request_title = request_title
        
        if description is not None:
            request.description = description
        
        if status is not None:
            request.status = status
        
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    async def add_files_to_request(
        db: Session,
        request_id: int,
        files: List[UploadFile]
    ) -> List[File]:
        """Add files to a request"""
        # Validate file count
        FileHandler.validate_file_count(files)
        
        # Check if request exists
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            raise HTTPException(status_code=404, detail="Request not found")
        
        # Check current file count
        current_file_count = db.query(File).filter(File.request_id == request_id).count()
        if current_file_count + len(files) > 5:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot add {len(files)} files. Maximum 5 files per request. Current: {current_file_count}"
            )
        
        saved_files = []
        
        try:
            for file in files:
                # Save file to disk
                file_info = await FileHandler.save_file(file, request_id)
                
                # Create file record in database
                db_file = File(
                    original_filename=file_info["original_filename"],
                    stored_filename=file_info["stored_filename"],
                    file_path=file_info["file_path"],
                    file_size=file_info["file_size"],
                    file_type=file_info["file_type"],
                    mime_type=file_info["mime_type"],
                    request_id=request_id
                )
                
                db.add(db_file)
                saved_files.append(db_file)
            
            db.commit()
            
            # Refresh all files
            for file in saved_files:
                db.refresh(file)
            
            return saved_files
            
        except Exception as e:
            # Rollback database changes
            db.rollback()
            
            # Clean up any saved files
            for file in saved_files:
                if hasattr(file, 'file_path'):
                    FileHandler.delete_file(file.file_path)
            
            raise e
    
    @staticmethod
    def delete_file_from_request(db: Session, file_id: int) -> bool:
        """Delete a file from a request"""
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            return False
        
        # Delete file from disk
        FileHandler.delete_file(file.file_path)
        
        # Delete file record from database
        db.delete(file)
        db.commit()
        
        return True

    @staticmethod
    def archive_request(db: Session, request_id: int) -> bool:
        """Archive request (soft delete)"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return False

        request.is_archived = True
        db.commit()

        return True

    @staticmethod
    def restore_request(db: Session, request_id: int) -> bool:
        """Restore archived request"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return False

        request.is_archived = False
        db.commit()

        return True

    @staticmethod
    def get_archived_requests(
        db: Session,
        skip: int = 0,
        limit: int = 50,
        user_id: Optional[int] = None
    ) -> List[Request]:
        """Get archived requests"""
        query = db.query(Request).filter(Request.is_archived == True)

        if user_id:
            query = query.filter(Request.user_id == user_id)

        return query.order_by(desc(Request.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_request_statistics(db: Session) -> dict:
        """Get request statistics"""
        total_requests = db.query(Request).count()
        pending_requests = db.query(Request).filter(Request.status == RequestStatus.PENDING).count()
        in_progress_requests = db.query(Request).filter(Request.status == RequestStatus.IN_PROGRESS).count()
        completed_requests = db.query(Request).filter(Request.status == RequestStatus.COMPLETED).count()
        rejected_requests = db.query(Request).filter(Request.status == RequestStatus.REJECTED).count()

        return {
            "total": total_requests,
            "pending": pending_requests,
            "in_progress": in_progress_requests,
            "completed": completed_requests,
            "rejected": rejected_requests
        }

    @staticmethod
    def get_user_monthly_chart_data(db: Session, months_back: int = 12) -> Dict[str, Any]:
        """Get monthly completed requests data for users with 'user' role for chart display"""
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months_back * 30)

        # Get users with 'user' role only
        users_with_user_role = db.query(User).filter(
            User.role == UserRole.USER,
            User.is_active == True
        ).all()

        # Arabic month names
        arabic_months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]

        # Generate month labels for the last 12 months
        month_labels = []
        current_date = end_date
        for i in range(months_back):
            month_labels.insert(0, arabic_months[current_date.month - 1])
            # Move to previous month
            if current_date.month == 1:
                current_date = current_date.replace(year=current_date.year - 1, month=12)
            else:
                current_date = current_date.replace(month=current_date.month - 1)

        # Prepare datasets for each user
        datasets = []
        colors = [
            '#5e72e4', '#11cdef', '#2dce89', '#fb6340', '#f5365c',
            '#ffd600', '#36b9cc', '#6f42c1', '#e83e8c', '#fd7e14'
        ]

        for idx, user in enumerate(users_with_user_role):
            # Get completed requests for this user grouped by month
            monthly_data = []
            current_date = end_date

            for month_idx in range(months_back):
                # Calculate start and end of the month
                if current_date.month == 1:
                    month_start = current_date.replace(year=current_date.year - 1, month=12, day=1)
                    month_end = current_date.replace(day=1) - timedelta(days=1)
                else:
                    month_start = current_date.replace(month=current_date.month - 1, day=1)
                    if current_date.month == 12:
                        month_end = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
                    else:
                        month_end = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)

                # Count completed requests for this user in this month
                completed_count = db.query(Request).filter(
                    Request.user_id == user.id,
                    Request.status == RequestStatus.COMPLETED,
                    Request.created_at >= month_start,
                    Request.created_at <= month_end
                ).count()

                monthly_data.insert(0, completed_count)

                # Move to previous month
                if current_date.month == 1:
                    current_date = current_date.replace(year=current_date.year - 1, month=12)
                else:
                    current_date = current_date.replace(month=current_date.month - 1)

            # Only include users who have at least one completed request
            if sum(monthly_data) > 0:
                color = colors[idx % len(colors)]
                datasets.append({
                    'label': user.full_name,
                    'data': monthly_data,
                    'borderColor': color,
                    'backgroundColor': color + '20',  # Add transparency
                    'pointBackgroundColor': color,
                    'pointBorderColor': '#ffffff',
                    'pointBorderWidth': 2,
                    'pointRadius': 6,
                    'pointHoverRadius': 8,
                    'borderWidth': 3,
                    'fill': False,
                    'tension': 0.4,
                    'user_id': user.id,
                    'username': user.username
                })

        return {
            'labels': month_labels,
            'datasets': datasets
        }

    @staticmethod
    def get_user_progress_data(db: Session) -> List[Dict[str, Any]]:
        """Get individual user progress tracking data for daily, weekly, and monthly goals"""
        # Get users with 'user' role only
        users_with_user_role = db.query(User).filter(
            User.role == UserRole.USER,
            User.is_active == True
        ).all()

        # Current time calculations
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Week calculations (Monday to Sunday)
        days_since_monday = now.weekday()
        week_start = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
        week_end = (week_start + timedelta(days=6)).replace(hour=23, minute=59, second=59, microsecond=999999)

        # Month calculations
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if now.month == 12:
            month_end = now.replace(year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)
        else:
            month_end = now.replace(month=now.month + 1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)

        # Goals configuration
        DAILY_GOAL = 10
        WEEKLY_GOAL = 50
        MONTHLY_GOAL = 200

        user_progress_list = []

        for user in users_with_user_role:
            # Count completed requests for each time period
            daily_completed = db.query(Request).filter(
                Request.user_id == user.id,
                Request.status == RequestStatus.COMPLETED,
                Request.created_at >= today_start,
                Request.created_at <= today_end
            ).count()

            weekly_completed = db.query(Request).filter(
                Request.user_id == user.id,
                Request.status == RequestStatus.COMPLETED,
                Request.created_at >= week_start,
                Request.created_at <= week_end
            ).count()

            monthly_completed = db.query(Request).filter(
                Request.user_id == user.id,
                Request.status == RequestStatus.COMPLETED,
                Request.created_at >= month_start,
                Request.created_at <= month_end
            ).count()

            # Calculate percentages
            daily_percentage = min(100, (daily_completed / DAILY_GOAL) * 100)
            weekly_percentage = min(100, (weekly_completed / WEEKLY_GOAL) * 100)
            monthly_percentage = min(100, (monthly_completed / MONTHLY_GOAL) * 100)

            # Calculate remaining requests needed to achieve goals
            daily_remaining = max(0, DAILY_GOAL - daily_completed)
            weekly_remaining = max(0, WEEKLY_GOAL - weekly_completed)
            monthly_remaining = max(0, MONTHLY_GOAL - monthly_completed)

            # Function to format remaining requests text in Arabic
            def format_remaining_text(remaining, goal_type):
                if remaining == 0:
                    return "تم تحقيق الهدف!"
                elif remaining == 1:
                    return f"طلب واحد متبقي لتحقيق الهدف {goal_type}"
                elif remaining == 2:
                    return f"طلبان متبقيان لتحقيق الهدف {goal_type}"
                elif 3 <= remaining <= 10:
                    return f"{remaining} طلبات متبقية لتحقيق الهدف {goal_type}"
                else:
                    return f"{remaining} طلباً متبقياً لتحقيق الهدف {goal_type}"

            # Determine status colors based on progress
            def get_status_color(percentage):
                if percentage >= 100:
                    return 'success'  # Green - Goal achieved
                elif percentage >= 70:
                    return 'warning'  # Yellow - On track
                else:
                    return 'danger'   # Red - Behind schedule

            user_progress_list.append({
                'user_id': user.id,
                'full_name': user.full_name,
                'username': user.username,
                'daily': {
                    'completed': daily_completed,
                    'goal': DAILY_GOAL,
                    'percentage': round(daily_percentage, 1),
                    'status': get_status_color(daily_percentage),
                    'remaining': daily_remaining,
                    'remaining_text': format_remaining_text(daily_remaining, "اليومي")
                },
                'weekly': {
                    'completed': weekly_completed,
                    'goal': WEEKLY_GOAL,
                    'percentage': round(weekly_percentage, 1),
                    'status': get_status_color(weekly_percentage),
                    'remaining': weekly_remaining,
                    'remaining_text': format_remaining_text(weekly_remaining, "الأسبوعي")
                },
                'monthly': {
                    'completed': monthly_completed,
                    'goal': MONTHLY_GOAL,
                    'percentage': round(monthly_percentage, 1),
                    'status': get_status_color(monthly_percentage),
                    'remaining': monthly_remaining,
                    'remaining_text': format_remaining_text(monthly_remaining, "الشهري")
                }
            })

        return user_progress_list

    @staticmethod
    def get_single_user_progress_data(db: Session, user_id: int) -> Dict[str, Any]:
        """Get progress tracking data for a single user"""
        # Get the user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None

        # Current time calculations
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Week calculations (Monday to Sunday)
        days_since_monday = now.weekday()
        week_start = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
        week_end = (week_start + timedelta(days=6)).replace(hour=23, minute=59, second=59, microsecond=999999)

        # Month calculations
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if now.month == 12:
            month_end = now.replace(year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)
        else:
            month_end = now.replace(month=now.month + 1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)

        # Goals configuration
        DAILY_GOAL = 10
        WEEKLY_GOAL = 50
        MONTHLY_GOAL = 200

        # Count completed requests for each time period
        daily_completed = db.query(Request).filter(
            Request.user_id == user_id,
            Request.status == RequestStatus.COMPLETED,
            Request.created_at >= today_start,
            Request.created_at <= today_end
        ).count()

        weekly_completed = db.query(Request).filter(
            Request.user_id == user_id,
            Request.status == RequestStatus.COMPLETED,
            Request.created_at >= week_start,
            Request.created_at <= week_end
        ).count()

        monthly_completed = db.query(Request).filter(
            Request.user_id == user_id,
            Request.status == RequestStatus.COMPLETED,
            Request.created_at >= month_start,
            Request.created_at <= month_end
        ).count()

        # Calculate percentages
        daily_percentage = min(100, (daily_completed / DAILY_GOAL) * 100)
        weekly_percentage = min(100, (weekly_completed / WEEKLY_GOAL) * 100)
        monthly_percentage = min(100, (monthly_completed / MONTHLY_GOAL) * 100)

        # Calculate remaining requests needed to achieve goals
        daily_remaining = max(0, DAILY_GOAL - daily_completed)
        weekly_remaining = max(0, WEEKLY_GOAL - weekly_completed)
        monthly_remaining = max(0, MONTHLY_GOAL - monthly_completed)

        # Function to format remaining requests text in Arabic
        def format_remaining_text(remaining, goal_type):
            if remaining == 0:
                return "تم تحقيق الهدف!"
            elif remaining == 1:
                return f"طلب واحد متبقي لتحقيق الهدف {goal_type}"
            elif remaining == 2:
                return f"طلبان متبقيان لتحقيق الهدف {goal_type}"
            elif 3 <= remaining <= 10:
                return f"{remaining} طلبات متبقية لتحقيق الهدف {goal_type}"
            else:
                return f"{remaining} طلباً متبقياً لتحقيق الهدف {goal_type}"

        # Determine status colors based on progress
        def get_status_color(percentage):
            if percentage >= 100:
                return 'success'  # Green - Goal achieved
            elif percentage >= 70:
                return 'warning'  # Yellow - On track
            else:
                return 'danger'   # Red - Behind schedule

        return {
            'user_id': user.id,
            'full_name': user.full_name,
            'username': user.username,
            'daily': {
                'completed': daily_completed,
                'goal': DAILY_GOAL,
                'percentage': round(daily_percentage, 1),
                'status': get_status_color(daily_percentage),
                'remaining': daily_remaining,
                'remaining_text': format_remaining_text(daily_remaining, "اليومي")
            },
            'weekly': {
                'completed': weekly_completed,
                'goal': WEEKLY_GOAL,
                'percentage': round(weekly_percentage, 1),
                'status': get_status_color(weekly_percentage),
                'remaining': weekly_remaining,
                'remaining_text': format_remaining_text(weekly_remaining, "الأسبوعي")
            },
            'monthly': {
                'completed': monthly_completed,
                'goal': MONTHLY_GOAL,
                'percentage': round(monthly_percentage, 1),
                'status': get_status_color(monthly_percentage),
                'remaining': monthly_remaining,
                'remaining_text': format_remaining_text(monthly_remaining, "الشهري")
            }
        }
