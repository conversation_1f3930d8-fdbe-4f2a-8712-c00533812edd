{% extends "base.html" %}

{% block title %}نشاطات النظام - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-activity"></i>
        نشاطات النظام
    </h2>
</div>

<div class="card">
    <div class="card-body">
        {% if activities %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>النوع</th>
                        <th>المستخدم</th>
                        <th>الوصف</th>
                        <th>عنوان IP</th>
                        <th>التاريخ والوقت</th>
                    </tr>
                </thead>
                <tbody>
                    {% for activity in activities %}
                    <tr>
                        <td>
                            {% if activity.activity_type.value == 'login' %}
                            <span class="badge bg-success">
                                <i class="bi bi-box-arrow-in-right"></i> دخول
                            </span>
                            {% elif activity.activity_type.value == 'logout' %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-box-arrow-right"></i> خروج
                            </span>
                            {% elif activity.activity_type.value == 'request_created' %}
                            <span class="badge bg-primary">
                                <i class="bi bi-plus-circle"></i> طلب جديد
                            </span>
                            {% elif activity.activity_type.value == 'request_updated' %}
                            <span class="badge bg-info">
                                <i class="bi bi-pencil"></i> تحديث طلب
                            </span>
                            {% elif activity.activity_type.value == 'file_uploaded' %}
                            <span class="badge bg-warning">
                                <i class="bi bi-upload"></i> رفع ملف
                            </span>
                            {% elif activity.activity_type.value == 'file_deleted' %}
                            <span class="badge bg-danger">
                                <i class="bi bi-trash"></i> حذف ملف
                            </span>
                            {% elif activity.activity_type.value == 'profile_updated' %}
                            <span class="badge bg-info">
                                <i class="bi bi-person"></i> تحديث الملف
                            </span>
                            {% elif activity.activity_type.value == 'password_changed' %}
                            <span class="badge bg-warning">
                                <i class="bi bi-key"></i> تغيير كلمة المرور
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-activity"></i> {{ activity.activity_type.value }}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ activity.user.full_name }}</strong>
                            <br>
                            <small class="text-muted">{{ activity.user.username }}</small>
                        </td>
                        <td>{{ activity.description }}</td>
                        <td>
                            {% if activity.ip_address %}
                            <code>{{ activity.ip_address }}</code>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ activity.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-activity display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا توجد نشاطات</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
