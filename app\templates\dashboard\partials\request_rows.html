{% for req in requests %}
<tr>
    <td>
        <code>{{ req.request_number }}</code>
    </td>
    <td>{{ req.request_title }}</td>
    <td>
        {% if req.status.value == 'pending' %}
        <span class="badge bg-warning">قيد المراجعة</span>
        {% elif req.status.value == 'in_progress' %}
        <span class="badge bg-info">قيد التنفيذ</span>
        {% elif req.status.value == 'completed' %}
        <span class="badge bg-success">مكتمل</span>
        {% elif req.status.value == 'rejected' %}
        <span class="badge bg-danger">مرفوض</span>
        {% endif %}
    </td>
    <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
    <td>
        <div class="d-flex gap-2">
            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-eye"></i>
                عرض
            </a>
            {% if req.status.value == 'pending' %}
            <a href="/requests/{{ req.id }}/edit" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-pencil-square"></i>
                تعديل
            </a>
            {% endif %}
        </div>
    </td>
</tr>
{% endfor %}

{% if requests %}
<!-- Load More Button Row -->
<tr id="load-more-row">
    <td colspan="5" class="text-center py-4">
        <button
            class="btn btn-outline-primary btn-sm px-4 py-2 fw-semibold"
            hx-get="/api/requests/load-more?skip={{ next_skip }}"
            hx-target="#requests-tbody"
            hx-swap="beforeend"
            hx-indicator="#user-loading-indicator"
            onclick="this.closest('tr').remove()">
            <i class="bi bi-arrow-down-circle me-2"></i>
            تحميل المزيد
        </button>
        <div id="user-loading-indicator" class="htmx-indicator ms-3 d-inline-block">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </td>
</tr>
{% endif %}
