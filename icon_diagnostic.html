<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Diagnostic Tool - CMSVS</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons with fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous" onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css';">
    
    <!-- Font Awesome as fallback -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem;
        }
        
        .diagnostic-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .icon-item {
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
        }
        
        .icon-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            transform: translateY(-2px);
        }
        
        .icon-display {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #5e72e4;
        }
        
        .icon-name {
            font-size: 0.875rem;
            color: #6c757d;
            font-family: monospace;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-loaded { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
        
        .diagnostic-info {
            background: #f8f9fa;
            border-left: 4px solid #5e72e4;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .btn-test {
            margin: 0.25rem;
        }
        
        /* Enhanced Icon Support */
        .bi {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            vertical-align: -.125em !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: inline-block !important;
            position: relative !important;
            z-index: 10 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-5">🔧 Icon Diagnostic Tool</h1>
        
        <!-- System Status -->
        <div class="diagnostic-card">
            <h3>📊 System Status</h3>
            <div class="diagnostic-info">
                <p><strong>Bootstrap Icons Status:</strong> <span id="icon-status">Checking...</span> <span id="status-indicator" class="status-indicator status-loading"></span></p>
                <p><strong>Font Detection:</strong> <span id="font-detection">Testing...</span></p>
                <p><strong>CDN Response:</strong> <span id="cdn-status">Testing...</span></p>
                <p><strong>Browser:</strong> <span id="browser-info">Loading...</span></p>
                <p><strong>RTL Support:</strong> <span id="rtl-status">Active</span></p>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="diagnostic-card">
            <h3>🛠️ Quick Actions</h3>
            <button class="btn btn-primary btn-test" onclick="testAllIcons()">Test All Icons</button>
            <button class="btn btn-secondary btn-test" onclick="refreshIcons()">Refresh Icons</button>
            <button class="btn btn-info btn-test" onclick="testCDN()">Test CDN</button>
            <button class="btn btn-warning btn-test" onclick="enableFallbacks()">Enable Fallbacks</button>
            <button class="btn btn-success btn-test" onclick="exportReport()">Export Report</button>
        </div>
        
        <!-- Dashboard Icons Test -->
        <div class="diagnostic-card">
            <h3>📋 Dashboard Icons Test</h3>
            <div id="dashboard-icons" class="icon-grid">
                <!-- Icons will be populated here -->
            </div>
        </div>
        
        <!-- Navigation Icons Test -->
        <div class="diagnostic-card">
            <h3>🧭 Navigation Icons Test</h3>
            <div id="navigation-icons" class="icon-grid">
                <!-- Icons will be populated here -->
            </div>
        </div>
        
        <!-- Icon Color Contrast Test -->
        <div class="diagnostic-card">
            <h3>🎨 Icon Color Contrast Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Navigation Style (Dark Background)</h5>
                    <div style="background: linear-gradient(135deg, #5e72e4 0%, #667eea 100%); padding: 1rem; border-radius: 0.5rem; color: white;">
                        <i class="bi bi-speedometer2 me-2" style="color: white;"></i>Dashboard
                        <i class="bi bi-people ms-3 me-2" style="color: white;"></i>Users
                        <i class="bi bi-file-earmark-text ms-3 me-2" style="color: white;"></i>Files
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Sidebar Style (Light Background)</h5>
                    <div style="background: white; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e9ecef;">
                        <div style="color: #8392ab; margin-bottom: 0.5rem;">
                            <i class="bi bi-house me-2" style="color: #8392ab;"></i>Home
                        </div>
                        <div style="color: #8392ab; margin-bottom: 0.5rem;">
                            <i class="bi bi-gear me-2" style="color: #8392ab;"></i>Settings
                        </div>
                        <div style="background: linear-gradient(135deg, #5e72e4 0%, #667eea 100%); color: white; padding: 0.5rem; border-radius: 0.5rem;">
                            <i class="bi bi-activity me-2" style="color: white;"></i>Active Item
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <h5>Statistics Card Style</h5>
                    <div style="background: linear-gradient(135deg, #2dce89 0%, #56ca00 100%); padding: 1rem; border-radius: 0.5rem; color: white;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4>150</h4>
                                <small>Total Items</small>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); width: 3rem; height: 3rem; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-check-circle" style="font-size: 1.5rem; color: white;"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Button Styles</h5>
                    <div>
                        <button class="btn btn-primary btn-sm mb-2">
                            <i class="bi bi-plus-circle me-1" style="color: white;"></i>Primary
                        </button><br>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye me-1" style="color: #5e72e4;"></i>Outline
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Card Header Style</h5>
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e9ecef;">
                        <h6 style="color: #32325d; margin: 0;">
                            <i class="bi bi-file-earmark-text me-2" style="color: #32325d;"></i>Card Title
                        </h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Icon Test -->
        <div class="diagnostic-card">
            <h3>🔧 Custom Icon Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="icon-input" class="form-label">Enter Icon Class:</label>
                    <input type="text" class="form-control" id="icon-input" placeholder="bi-house" value="bi-house">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Result:</label>
                    <div class="form-control d-flex align-items-center">
                        <i id="custom-icon" class="bi bi-house me-2" style="font-size: 1.5rem; color: #5e72e4;"></i>
                        <span id="custom-result">Ready</span>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary mt-2" onclick="testCustomIcon()">Test Icon</button>
            <button class="btn btn-info mt-2" onclick="testIconColors()">Test All Colors</button>
        </div>
        
        <!-- Diagnostic Log -->
        <div class="diagnostic-card">
            <h3>📝 Diagnostic Log</h3>
            <div id="diagnostic-log" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; font-family: monospace; font-size: 0.875rem;">
                <div>🔄 Initializing diagnostic tool...</div>
            </div>
            <button class="btn btn-outline-secondary btn-sm mt-2" onclick="clearLog()">Clear Log</button>
        </div>
    </div>
    
    <script>
        // Dashboard Icons List
        const dashboardIcons = [
            'bi-speedometer2', 'bi-person', 'bi-people', 'bi-file-earmark-text',
            'bi-clock', 'bi-gear', 'bi-check-circle', 'bi-arrow-up',
            'bi-arrow-left', 'bi-eye', 'bi-shield-check', 'bi-shield',
            'bi-person-check', 'bi-play-circle', 'bi-x-circle'
        ];
        
        // Navigation Icons List
        const navigationIcons = [
            'bi-house', 'bi-plus-circle', 'bi-person-circle', 'bi-box-arrow-right',
            'bi-box-arrow-in-right', 'bi-archive', 'bi-activity', 'bi-upload'
        ];
        
        let diagnosticLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            diagnosticLog.push(logEntry);
            
            const logContainer = document.getElementById('diagnostic-log');
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            logDiv.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function checkBootstrapIcons() {
            const testElement = document.createElement('i');
            testElement.className = 'bi bi-house';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.getPropertyValue('font-family');
            
            document.body.removeChild(testElement);
            
            return fontFamily.includes('bootstrap-icons');
        }
        
        function updateSystemStatus() {
            const statusElement = document.getElementById('icon-status');
            const indicatorElement = document.getElementById('status-indicator');
            const fontDetectionElement = document.getElementById('font-detection');
            const browserInfoElement = document.getElementById('browser-info');
            
            // Browser info
            browserInfoElement.textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
            
            // Icon status
            if (checkBootstrapIcons()) {
                statusElement.textContent = 'Bootstrap Icons Loaded Successfully ✓';
                indicatorElement.className = 'status-indicator status-loaded';
                fontDetectionElement.textContent = 'Bootstrap Icons font detected';
                log('Bootstrap Icons loaded successfully', 'success');
            } else {
                statusElement.textContent = 'Bootstrap Icons Failed to Load ✗';
                indicatorElement.className = 'status-indicator status-failed';
                fontDetectionElement.textContent = 'Bootstrap Icons font NOT detected';
                log('Bootstrap Icons failed to load', 'error');
            }
        }
        
        function createIconItem(iconClass) {
            const item = document.createElement('div');
            item.className = 'icon-item';
            
            const icon = document.createElement('i');
            icon.className = `bi ${iconClass}`;
            icon.classList.add('icon-display');
            
            const name = document.createElement('div');
            name.className = 'icon-name';
            name.textContent = iconClass;
            
            item.appendChild(icon);
            item.appendChild(name);
            
            return item;
        }
        
        window.testAllIcons = function() {
            log('Testing all dashboard icons...');
            
            // Dashboard icons
            const dashboardContainer = document.getElementById('dashboard-icons');
            dashboardContainer.innerHTML = '';
            dashboardIcons.forEach(iconClass => {
                dashboardContainer.appendChild(createIconItem(iconClass));
            });
            
            // Navigation icons
            const navigationContainer = document.getElementById('navigation-icons');
            navigationContainer.innerHTML = '';
            navigationIcons.forEach(iconClass => {
                navigationContainer.appendChild(createIconItem(iconClass));
            });
            
            log(`Rendered ${dashboardIcons.length + navigationIcons.length} icons`, 'success');
        };
        
        window.refreshIcons = function() {
            log('Refreshing icon system...');
            updateSystemStatus();
            const icons = document.querySelectorAll('.bi');
            icons.forEach(icon => {
                icon.style.fontFamily = '"bootstrap-icons", "Font Awesome 6 Free", system-ui, sans-serif';
            });
            log(`Refreshed ${icons.length} icons`, 'success');
        };
        
        window.testCDN = function() {
            log('Testing CDN connectivity...');
            const img = new Image();
            img.onload = () => {
                document.getElementById('cdn-status').textContent = 'CDN Accessible ✓';
                log('CDN is accessible', 'success');
            };
            img.onerror = () => {
                document.getElementById('cdn-status').textContent = 'CDN Error ✗';
                log('CDN connection failed', 'error');
            };
            img.src = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/fonts/bootstrap-icons.woff2';
        };
        
        window.testCustomIcon = function() {
            const input = document.getElementById('icon-input');
            const icon = document.getElementById('custom-icon');
            const result = document.getElementById('custom-result');

            const iconClass = input.value.trim();
            if (!iconClass) {
                result.textContent = 'Please enter an icon class';
                return;
            }

            icon.className = `bi ${iconClass} me-2`;
            icon.style.color = '#5e72e4';
            result.textContent = `Testing: ${iconClass}`;
            log(`Testing custom icon: ${iconClass}`);
        };

        window.testIconColors = function() {
            log('Testing icon color contrast...');

            // Test all icons on the page
            const icons = document.querySelectorAll('.bi');
            let colorIssues = 0;

            icons.forEach(icon => {
                const computedStyle = window.getComputedStyle(icon);
                const color = computedStyle.color;
                const backgroundColor = computedStyle.backgroundColor;

                // Check if icon is visible
                if (color === 'rgba(0, 0, 0, 0)' || color === 'transparent') {
                    colorIssues++;
                    log(`Color issue found: ${icon.className} has transparent color`, 'error');
                }

                // Force proper colors based on context
                const parent = icon.closest('.navbar, .card-stats, .avatar-lg, .btn, .list-group-item');
                if (parent) {
                    if (parent.classList.contains('navbar')) {
                        icon.style.color = 'white';
                    } else if (parent.classList.contains('card-stats') || parent.classList.contains('avatar-lg')) {
                        icon.style.color = 'white';
                    } else if (parent.classList.contains('btn-primary') || parent.classList.contains('btn-success') ||
                               parent.classList.contains('btn-info') || parent.classList.contains('btn-warning') ||
                               parent.classList.contains('btn-danger')) {
                        icon.style.color = 'white';
                    } else if (parent.classList.contains('btn-outline-primary')) {
                        icon.style.color = '#5e72e4';
                    } else if (parent.classList.contains('list-group-item')) {
                        if (parent.classList.contains('active')) {
                            icon.style.color = 'white';
                        } else {
                            icon.style.color = '#8392ab';
                        }
                    }
                }
            });

            if (colorIssues === 0) {
                log('All icons have proper color contrast ✓', 'success');
            } else {
                log(`Found ${colorIssues} color contrast issues`, 'error');
            }
        };
        
        window.enableFallbacks = function() {
            log('Enabling fallback icons...');
            document.body.classList.add('icons-fallback');
            log('Fallback mode enabled', 'success');
        };
        
        window.exportReport = function() {
            const report = {
                timestamp: new Date().toISOString(),
                iconsLoaded: checkBootstrapIcons(),
                browser: navigator.userAgent,
                diagnosticLog: diagnosticLog
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'icon-diagnostic-report.json';
            a.click();
            URL.revokeObjectURL(url);
            
            log('Diagnostic report exported', 'success');
        };
        
        window.clearLog = function() {
            diagnosticLog = [];
            document.getElementById('diagnostic-log').innerHTML = '<div>📝 Log cleared</div>';
        };
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Diagnostic tool initialized');
            updateSystemStatus();
            testAllIcons();
            testCDN();
            
            // Auto-refresh every 5 seconds
            setInterval(updateSystemStatus, 5000);
            
            // Custom icon input handler
            document.getElementById('icon-input').addEventListener('input', testCustomIcon);
        });
    </script>
</body>
</html>
