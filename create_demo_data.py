#!/usr/bin/env python3
"""
Create demo data for CMSVS Internal System
Run this script to populate the system with sample data for testing
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database import SessionLocal
from app.services.user_service import UserService
from app.services.request_service import RequestService
from app.models.user import UserRole
from app.models.request import RequestStatus
from app.models.activity import ActivityType

def create_demo_users(db):
    """Create demo users"""
    print("👥 Creating demo users...")
    
    demo_users = [
        {
            "username": "ahmed_hassan",
            "email": "<EMAIL>",
            "full_name": "أحمد حسن",
            "password": "password123",
            "role": UserRole.USER
        },
        {
            "username": "fatima_ali",
            "email": "<EMAIL>",
            "full_name": "فاطمة علي",
            "password": "password123",
            "role": UserRole.USER
        },
        {
            "username": "omar_salem",
            "email": "<EMAIL>",
            "full_name": "عمر سالم",
            "password": "password123",
            "role": UserRole.USER
        },
        {
            "username": "sara_mahmoud",
            "email": "<EMAIL>",
            "full_name": "سارة محمود",
            "password": "password123",
            "role": UserRole.USER
        },
        {
            "username": "test_user",
            "email": "<EMAIL>",
            "full_name": "مستخدم تجريبي",
            "password": "test123",
            "role": UserRole.USER
        }
    ]
    
    created_users = []
    
    for user_data in demo_users:
        try:
            # Check if user already exists
            existing_user = UserService.get_user_by_username(db, user_data["username"])
            if existing_user:
                print(f"⚠️  User {user_data['username']} already exists")
                created_users.append(existing_user)
            else:
                user = UserService.create_user(db, **user_data)
                print(f"✅ Created user: {user.username} ({user.full_name})")
                created_users.append(user)
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    return created_users

def create_demo_requests(db, users):
    """Create demo requests"""
    print("\n📋 Creating demo requests...")
    
    demo_requests = [
        {
            "request_name": "أرشفة وثائق المشروع الأول",
            "request_title": "أرشفة وثائق مشروع تطوير النظام الجديد",
            "description": "طلب أرشفة جميع الوثائق المتعلقة بمشروع تطوير النظام الجديد بما في ذلك المواصفات الفنية والتصاميم والاختبارات."
        },
        {
            "request_name": "أرشفة تقارير الربع الأول",
            "request_title": "أرشفة التقارير المالية للربع الأول من العام",
            "description": "أرشفة جميع التقارير المالية والإدارية للربع الأول من العام الحالي."
        },
        {
            "request_name": "أرشفة عقود الموردين",
            "request_title": "أرشفة عقود الموردين الجدد",
            "description": "طلب أرشفة عقود الموردين الجدد الموقعة خلال الشهر الماضي."
        },
        {
            "request_name": "أرشفة سجلات الموظفين",
            "request_title": "أرشفة ملفات الموظفين الجدد",
            "description": "أرشفة ملفات وسجلات الموظفين الجدد المنضمين للشركة."
        },
        {
            "request_name": "أرشفة وثائق التدريب",
            "request_title": "أرشفة مواد وشهادات التدريب",
            "description": "طلب أرشفة جميع مواد التدريب والشهادات الصادرة من البرامج التدريبية."
        }
    ]
    
    created_requests = []
    statuses = [RequestStatus.PENDING, RequestStatus.IN_PROGRESS, RequestStatus.COMPLETED, RequestStatus.REJECTED]
    
    for i, request_data in enumerate(demo_requests):
        try:
            # Assign to different users
            user = users[i % len(users)]
            
            request = RequestService.create_request(
                db=db,
                user_id=user.id,
                **request_data
            )
            
            # Set different statuses for demo
            if i < len(statuses):
                RequestService.update_request_status(db, request.id, statuses[i])
            
            print(f"✅ Created request: {request.request_number} - {request.request_title}")
            created_requests.append(request)
            
        except Exception as e:
            print(f"❌ Error creating request: {e}")
    
    return created_requests

def create_demo_activities(db, users):
    """Create demo activities"""
    print("\n📊 Creating demo activities...")
    
    demo_activities = [
        (ActivityType.LOGIN, "تسجيل دخول المستخدم"),
        (ActivityType.REQUEST_CREATED, "إنشاء طلب أرشفة جديد"),
        (ActivityType.FILE_UPLOADED, "رفع ملف جديد"),
        (ActivityType.PROFILE_UPDATED, "تحديث الملف الشخصي"),
        (ActivityType.REQUEST_UPDATED, "تحديث حالة الطلب"),
    ]
    
    for user in users:
        for activity_type, description in demo_activities:
            try:
                UserService.log_activity(
                    db=db,
                    user_id=user.id,
                    activity_type=activity_type,
                    description=f"{description} - {user.full_name}",
                    ip_address="*************",
                    user_agent="Mozilla/5.0 (Demo Data)"
                )
            except Exception as e:
                print(f"❌ Error creating activity: {e}")
    
    print("✅ Created demo activities")

def main():
    """Main function"""
    print("=" * 60)
    print("🎭 CMSVS Internal System - Demo Data Creator")
    print("=" * 60)
    print()
    print("⚠️  This will create sample data for testing purposes")
    print("   Make sure you have initialized the database first!")
    print()
    
    # Confirm before proceeding
    response = input("Do you want to continue? (y/N): ").lower().strip()
    if response != 'y':
        print("❌ Operation cancelled")
        return
    
    db = SessionLocal()
    try:
        # Create demo users
        users = create_demo_users(db)
        
        if not users:
            print("❌ No users created. Cannot proceed with requests.")
            return
        
        # Create demo requests
        requests = create_demo_requests(db, users)
        
        # Create demo activities
        create_demo_activities(db, users)
        
        print("\n" + "=" * 60)
        print("🎉 Demo data created successfully!")
        print("=" * 60)
        print()
        print("📝 Demo Users Created:")
        for user in users:
            print(f"   👤 {user.username} - {user.full_name}")
            print(f"      Email: {user.email}")
            print(f"      Password: password123")
            print()
        
        print("📋 Demo Requests Created:")
        for request in requests:
            print(f"   📄 {request.request_number} - {request.request_title}")
            print(f"      Status: {request.status.value}")
            print(f"      User: {request.user.full_name}")
            print()
        
        print("🚀 You can now:")
        print("1. Start the application: python run.py")
        print("2. Login with any demo user (password: password123)")
        print("3. Or login as admin to manage the system")
        print()
        
    except Exception as e:
        print(f"❌ Error creating demo data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
