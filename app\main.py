from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session
import os
import logging

from app.config import settings
from app.database import create_tables, get_db
from app.routes import auth, dashboard, admin
from app.services.user_service import UserService
from app.models.user import UserRole
from app.utils.auth import verify_token

# Configure logging
logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
os.makedirs("app/static", exist_ok=True)
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Templates
templates = Jinja2Templates(directory="app/templates")

# Security
security = HTTPBearer(auto_error=False)


async def get_current_user_from_cookie(request: Request, db: Session = Depends(get_db)):
    """Get current user from cookie"""
    token = request.cookies.get("access_token")
    if not token:
        return None
    
    # Remove 'Bearer ' prefix if present
    if token.startswith("Bearer "):
        token = token[7:]
    
    payload = verify_token(token)
    if not payload:
        return None
    
    username = payload.get("sub")
    if not username:
        return None
    
    user = UserService.get_user_by_username(db, username)
    return user


# Include routers
app.include_router(auth.router, tags=["authentication"])
app.include_router(dashboard.router, tags=["dashboard"])
app.include_router(admin.router, tags=["admin"])


@app.on_event("startup")
async def startup_event():
    """Initialize application"""
    logger.info("Starting CMSVS Internal System...")
    
    # Create database tables
    create_tables()
    logger.info("Database tables created/verified")
    
    # Create default admin user if it doesn't exist
    from app.database import SessionLocal
    db = SessionLocal()
    try:
        admin_user = UserService.get_user_by_email(db, settings.admin_email)
        if not admin_user:
            UserService.create_user(
                db=db,
                username="admin",
                email=settings.admin_email,
                full_name="System Administrator",
                password=settings.admin_password,
                role=UserRole.ADMIN
            )
            logger.info("Default admin user created")
        else:
            logger.info("Admin user already exists")
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
    finally:
        db.close()
    
    logger.info("Application startup complete")


@app.get("/", response_class=HTMLResponse)
async def root(request: Request, db: Session = Depends(get_db)):
    """Root endpoint - redirect to appropriate dashboard"""
    current_user = await get_current_user_from_cookie(request, db)
    
    if not current_user:
        return RedirectResponse(url="/login")
    
    if current_user.role == UserRole.ADMIN:
        return RedirectResponse(url="/admin/dashboard")
    else:
        return RedirectResponse(url="/dashboard")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.app_version}


@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors"""
    current_user = await get_current_user_from_cookie(request, get_db().__next__())
    return templates.TemplateResponse(
        "errors/404.html",
        {"request": request, "current_user": current_user},
        status_code=404
    )


@app.exception_handler(403)
async def forbidden_handler(request: Request, exc: HTTPException):
    """Handle 403 errors"""
    current_user = await get_current_user_from_cookie(request, get_db().__next__())
    return templates.TemplateResponse(
        "errors/403.html",
        {"request": request, "current_user": current_user},
        status_code=403
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: HTTPException):
    """Handle 500 errors"""
    current_user = await get_current_user_from_cookie(request, get_db().__next__())
    return templates.TemplateResponse(
        "errors/500.html",
        {"request": request, "current_user": current_user},
        status_code=500
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
