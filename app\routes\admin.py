from fastapi import APIRouter, Depends, Request, Form, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from typing import Optional, List
from app.database import get_db
from app.utils.auth import verify_token
from app.services.user_service import UserService
from app.services.request_service import RequestService
from app.models.user import User, UserRole
from app.models.request import RequestStatus
from app.models.activity import Activity, ActivityType

router = APIRouter(prefix="/admin")
templates = Jinja2Templates(directory="app/templates")


async def require_admin_cookie(request: Request, db: Session = Depends(get_db)) -> User:
    """Require admin role using cookie authentication"""
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=403, detail="Not authenticated")

    # Remove 'Bearer ' prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=403, detail="Invalid token")

    username = payload.get("sub")
    if not username:
        raise HTTPException(status_code=403, detail="Invalid token")

    user = UserService.get_user_by_username(db, username)
    if not user:
        raise HTTPException(status_code=403, detail="User not found")

    if not user.is_active:
        raise HTTPException(status_code=403, detail="Inactive user")

    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    return user


@router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Admin dashboard"""
    # Get system statistics
    request_stats = RequestService.get_request_statistics(db)

    # Get recent requests
    recent_requests = RequestService.get_all_requests(db, limit=10)

    # Get all users count
    all_users = UserService.get_all_users(db, limit=1000)
    user_count = len(all_users)
    admin_count = len([u for u in all_users if u.role == UserRole.ADMIN])
    active_users = len([u for u in all_users if u.is_active])

    # Get user monthly chart data
    user_chart_data = RequestService.get_user_monthly_chart_data(db)

    # Get user progress tracking data
    user_progress_data = RequestService.get_user_progress_data(db)

    return templates.TemplateResponse(
        "admin/dashboard.html",
        {
            "request": request,
            "current_user": current_user,
            "request_stats": request_stats,
            "recent_requests": recent_requests,
            "user_stats": {
                "total": user_count,
                "admins": admin_count,
                "active": active_users
            },
            "user_chart_data": user_chart_data,
            "user_progress_data": user_progress_data
        }
    )


@router.get("/users", response_class=HTMLResponse)
async def manage_users(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Manage users page"""
    users = UserService.get_all_users(db, limit=100)
    
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users
        }
    )


@router.get("/users/new", response_class=HTMLResponse)
async def new_user_form(
    request: Request,
    current_user: User = Depends(require_admin_cookie)
):
    """Display new user creation form"""
    return templates.TemplateResponse(
        "admin/new_user.html",
        {
            "request": request,
            "current_user": current_user,
            "roles": [role.value for role in UserRole]
        }
    )


@router.post("/users/new")
async def create_new_user(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Create new user"""
    try:
        # Validate role
        try:
            user_role = UserRole(role)
        except ValueError:
            return templates.TemplateResponse(
                "admin/new_user.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "roles": [role.value for role in UserRole],
                    "error": "Invalid role selected"
                },
                status_code=400
            )

        # Create user
        new_user = UserService.create_user(
            db=db,
            username=username,
            email=email,
            full_name=full_name,
            password=password,
            role=user_role
        )

        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.PROFILE_UPDATED,
            description=f"Admin created new user: {username} with role {role}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

        users = UserService.get_all_users(db, limit=100)
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": users,
                "success": f"User {username} created successfully"
            }
        )

    except HTTPException as e:
        return templates.TemplateResponse(
            "admin/new_user.html",
            {
                "request": request,
                "current_user": current_user,
                "roles": [role.value for role in UserRole],
                "error": e.detail,
                "form_data": {
                    "username": username,
                    "email": email,
                    "full_name": full_name,
                    "role": role
                }
            },
            status_code=400
        )


@router.post("/users/{user_id}/toggle-status")
async def toggle_user_status(
    request: Request,
    user_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Toggle user active status"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Don't allow deactivating self
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "Cannot deactivate your own account"
            },
            status_code=400
        )

    # Toggle status
    UserService.update_user(db, user_id, is_active=not user.is_active)

    # Log activity
    action = "activated" if not user.is_active else "deactivated"
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.PROFILE_UPDATED,
        description=f"Admin {action} user: {user.username}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )

    users = UserService.get_all_users(db, limit=100)
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users,
            "success": f"User {user.username} has been {action}"
        }
    )


@router.post("/users/{user_id}/update-role")
async def update_user_role(
    request: Request,
    user_id: int,
    role: str = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Update user role"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Don't allow changing own role
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "Cannot change your own role"
            },
            status_code=400
        )

    # Validate role
    try:
        new_role = UserRole(role)
    except ValueError:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "Invalid role selected"
            },
            status_code=400
        )

    # Update role
    UserService.update_user(db, user_id, role=new_role)

    # Log activity
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.PROFILE_UPDATED,
        description=f"Admin changed user {user.username} role to {new_role.value}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )

    users = UserService.get_all_users(db, limit=100)
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users,
            "success": f"User {user.username} role updated to {new_role.value}"
        }
    )


@router.get("/users/{user_id}/edit", response_class=HTMLResponse)
async def edit_user_form(
    request: Request,
    user_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Display user edit form"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    return templates.TemplateResponse(
        "admin/edit_user.html",
        {
            "request": request,
            "current_user": current_user,
            "user": user,
            "roles": [role.value for role in UserRole]
        }
    )


@router.post("/users/{user_id}/edit")
async def update_user_profile(
    request: Request,
    user_id: int,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    role: str = Form(...),
    is_active: bool = Form(False),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Don't allow editing own account through this interface
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/edit_user.html",
            {
                "request": request,
                "current_user": current_user,
                "user": user,
                "roles": [role.value for role in UserRole],
                "error": "Cannot edit your own account through this interface"
            },
            status_code=400
        )

    try:
        # Validate role
        try:
            user_role = UserRole(role)
        except ValueError:
            return templates.TemplateResponse(
                "admin/edit_user.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "user": user,
                    "roles": [role.value for role in UserRole],
                    "error": "Invalid role selected"
                },
                status_code=400
            )

        # Update user
        updated_user = UserService.update_user(
            db=db,
            user_id=user_id,
            username=username,
            email=email,
            full_name=full_name,
            role=user_role,
            is_active=is_active
        )

        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.PROFILE_UPDATED,
            description=f"Admin updated user profile: {username}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

        users = UserService.get_all_users(db, limit=100)
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": users,
                "success": f"User {username} profile updated successfully"
            }
        )

    except HTTPException as e:
        return templates.TemplateResponse(
            "admin/edit_user.html",
            {
                "request": request,
                "current_user": current_user,
                "user": user,
                "roles": [role.value for role in UserRole],
                "error": e.detail
            },
            status_code=400
        )


@router.post("/users/{user_id}/reset-password")
async def reset_user_password(
    request: Request,
    user_id: int,
    new_password: str = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Reset user password"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Don't allow resetting own password through this interface
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/edit_user.html",
            {
                "request": request,
                "current_user": current_user,
                "user": user,
                "roles": [role.value for role in UserRole],
                "error": "Cannot reset your own password through this interface"
            },
            status_code=400
        )

    # Reset password
    success = UserService.change_password(db, user_id, new_password)

    if not success:
        return templates.TemplateResponse(
            "admin/edit_user.html",
            {
                "request": request,
                "current_user": current_user,
                "user": user,
                "roles": [role.value for role in UserRole],
                "error": "Failed to reset password"
            },
            status_code=500
        )

    # Log activity
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.PROFILE_UPDATED,
        description=f"Admin reset password for user: {user.username}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )

    return templates.TemplateResponse(
        "admin/edit_user.html",
        {
            "request": request,
            "current_user": current_user,
            "user": user,
            "roles": [role.value for role in UserRole],
            "success": f"Password reset successfully for user {user.username}"
        }
    )


@router.post("/users/{user_id}/soft-delete")
async def soft_delete_user(
    request: Request,
    user_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Soft delete user (deactivate)"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Don't allow soft deleting self
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "Cannot delete your own account"
            },
            status_code=400
        )

    # Soft delete user
    success = UserService.soft_delete_user(db, user_id)

    if success:
        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.PROFILE_UPDATED,
            description=f"Admin soft deleted user: {user.username}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

    users = UserService.get_all_users(db, limit=100)
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users,
            "success": f"User {user.username} has been deactivated" if success else None,
            "error": "Failed to deactivate user" if not success else None
        }
    )


@router.get("/archived-requests", response_class=HTMLResponse)
async def view_archived_requests(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """View archived requests"""
    archived_requests = RequestService.get_archived_requests(db, limit=100)

    return templates.TemplateResponse(
        "admin/archived_requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": archived_requests
        }
    )


@router.post("/requests/{request_id}/archive")
async def archive_request(
    request: Request,
    request_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Archive request"""
    req = RequestService.get_request_by_id(db, request_id)
    if not req:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Archive request
    success = RequestService.archive_request(db, request_id)

    if success:
        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.REQUEST_UPDATED,
            description=f"Admin archived request {req.request_number}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

    requests = RequestService.get_all_requests(db, limit=100)
    return templates.TemplateResponse(
        "admin/requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests,
            "success": f"Request {req.request_number} has been archived" if success else None,
            "error": "Failed to archive request" if not success else None
        }
    )


@router.post("/requests/{request_id}/restore")
async def restore_request(
    request: Request,
    request_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Restore archived request"""
    req = RequestService.get_request_by_id(db, request_id)
    if not req:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )

    # Restore request
    success = RequestService.restore_request(db, request_id)

    if success:
        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.REQUEST_UPDATED,
            description=f"Admin restored request {req.request_number}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

    archived_requests = RequestService.get_archived_requests(db, limit=100)
    return templates.TemplateResponse(
        "admin/archived_requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": archived_requests,
            "success": f"Request {req.request_number} has been restored" if success else None,
            "error": "Failed to restore request" if not success else None
        }
    )


@router.post("/users/bulk-action")
async def bulk_user_action(
    request: Request,
    action: str = Form(...),
    user_ids: List[int] = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Perform bulk action on users"""
    if not user_ids:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "لم يتم اختيار أي مستخدمين"
            },
            status_code=400
        )

    # Don't allow bulk actions on self
    if current_user.id in user_ids:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "لا يمكن تطبيق العمليات المجمعة على حسابك الشخصي"
            },
            status_code=400
        )

    success_count = 0
    total_count = len(user_ids)

    try:
        for user_id in user_ids:
            user = UserService.get_user_by_id(db, user_id)
            if not user or user.id == current_user.id:
                continue

            if action == "activate":
                UserService.update_user(db, user_id, is_active=True)
                success_count += 1
            elif action == "deactivate":
                UserService.update_user(db, user_id, is_active=False)
                success_count += 1
            elif action == "make_admin":
                UserService.update_user(db, user_id, role=UserRole.ADMIN)
                success_count += 1
            elif action == "make_user":
                UserService.update_user(db, user_id, role=UserRole.USER)
                success_count += 1

        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.PROFILE_UPDATED,
            description=f"Admin performed bulk action '{action}' on {success_count} users",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

        action_names = {
            "activate": "تفعيل",
            "deactivate": "إلغاء تفعيل",
            "make_admin": "ترقية إلى مدير",
            "make_user": "تحويل إلى مستخدم عادي"
        }

        users = UserService.get_all_users(db, limit=100)
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": users,
                "success": f"تم {action_names.get(action, action)} {success_count} من أصل {total_count} مستخدم"
            }
        )

    except Exception as e:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": f"حدث خطأ أثناء تنفيذ العملية: {str(e)}"
            },
            status_code=500
        )


@router.post("/requests/bulk-action")
async def bulk_request_action(
    request: Request,
    action: str = Form(...),
    request_ids: List[int] = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Perform bulk action on requests"""
    if not request_ids:
        return templates.TemplateResponse(
            "admin/requests.html",
            {
                "request": request,
                "current_user": current_user,
                "requests": RequestService.get_all_requests(db, limit=100),
                "statuses": [s.value for s in RequestStatus],
                "error": "لم يتم اختيار أي طلبات"
            },
            status_code=400
        )

    success_count = 0
    total_count = len(request_ids)

    try:
        for request_id in request_ids:
            req = RequestService.get_request_by_id(db, request_id)
            if not req:
                continue

            if action == "pending":
                RequestService.update_request(db, request_id, status=RequestStatus.PENDING)
                success_count += 1
            elif action == "in_progress":
                RequestService.update_request(db, request_id, status=RequestStatus.IN_PROGRESS)
                success_count += 1
            elif action == "completed":
                RequestService.update_request(db, request_id, status=RequestStatus.COMPLETED)
                success_count += 1
            elif action == "rejected":
                RequestService.update_request(db, request_id, status=RequestStatus.REJECTED)
                success_count += 1
            elif action == "archive":
                RequestService.archive_request(db, request_id)
                success_count += 1

        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.REQUEST_UPDATED,
            description=f"Admin performed bulk action '{action}' on {success_count} requests",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )

        action_names = {
            "pending": "تحويل إلى قيد المراجعة",
            "in_progress": "تحويل إلى قيد التنفيذ",
            "completed": "تحويل إلى مكتمل",
            "rejected": "تحويل إلى مرفوض",
            "archive": "أرشفة"
        }

        requests = RequestService.get_all_requests(db, limit=100)
        return templates.TemplateResponse(
            "admin/requests.html",
            {
                "request": request,
                "current_user": current_user,
                "requests": requests,
                "statuses": [s.value for s in RequestStatus],
                "success": f"تم {action_names.get(action, action)} {success_count} من أصل {total_count} طلب"
            }
        )

    except Exception as e:
        return templates.TemplateResponse(
            "admin/requests.html",
            {
                "request": request,
                "current_user": current_user,
                "requests": RequestService.get_all_requests(db, limit=100),
                "statuses": [s.value for s in RequestStatus],
                "error": f"حدث خطأ أثناء تنفيذ العملية: {str(e)}"
            },
            status_code=500
        )


@router.get("/requests", response_class=HTMLResponse)
async def manage_requests(
    request: Request,
    status: Optional[str] = None,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Manage requests page"""
    # Parse status filter
    status_filter = None
    if status:
        try:
            status_filter = RequestStatus(status)
        except ValueError:
            pass
    
    requests = RequestService.get_all_requests(db, limit=100, status=status_filter)
    
    return templates.TemplateResponse(
        "admin/requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests,
            "current_status": status,
            "statuses": [s.value for s in RequestStatus]
        }
    )


@router.post("/requests/{request_id}/update-status")
async def update_request_status(
    request: Request,
    request_id: int,
    status: str = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Update request status"""
    try:
        new_status = RequestStatus(status)
    except ValueError:
        return templates.TemplateResponse(
            "admin/requests.html",
            {
                "request": request,
                "current_user": current_user,
                "requests": RequestService.get_all_requests(db, limit=100),
                "error": "Invalid status"
            },
            status_code=400
        )
    
    req = RequestService.update_request_status(db, request_id, new_status)
    if not req:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )
    
    # Log activity
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.REQUEST_UPDATED,
        description=f"Admin updated request {req.request_number} status to {new_status.value}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )
    
    requests = RequestService.get_all_requests(db, limit=100)
    return templates.TemplateResponse(
        "admin/requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests,
            "success": f"Request {req.request_number} status updated to {new_status.value}"
        }
    )


@router.get("/api/requests/load-more", response_class=HTMLResponse)
async def admin_load_more_requests(
    request: Request,
    skip: int = 10,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """HTMX endpoint to load more requests for admin dashboard"""
    # Load next batch of requests
    additional_requests = RequestService.get_all_requests(
        db, skip=skip, limit=10
    )

    return templates.TemplateResponse(
        "admin/partials/request_rows.html",
        {
            "request": request,
            "current_user": current_user,
            "recent_requests": additional_requests,
            "next_skip": skip + 10
        }
    )


@router.get("/activities", response_class=HTMLResponse)
async def view_activities(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """View system activities"""
    # Get recent activities from all users
    activities = db.query(Activity).order_by(Activity.created_at.desc()).limit(100).all()

    return templates.TemplateResponse(
        "admin/activities.html",
        {
            "request": request,
            "current_user": current_user,
            "activities": activities
        }
    )






