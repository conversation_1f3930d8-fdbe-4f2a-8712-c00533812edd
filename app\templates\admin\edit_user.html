{% extends "base.html" %}

{% block title %}تعديل المستخدم {{ user.username }} - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-person-gear"></i>
        تعديل المستخدم
    </h2>
    <a href="/admin/users" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i>
        العودة لإدارة المستخدمين
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- User Profile Edit -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 py-4">
                <h4 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-person-gear text-primary me-2"></i>
                    تعديل بيانات المستخدم
                </h4>
                <p class="text-muted mb-0 small">تعديل المعلومات الأساسية للمستخدم</p>
            </div>
            <div class="card-body p-4">
                <form method="post" action="/admin/users/{{ user.id }}/edit" id="editUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person text-primary me-1"></i>
                                    اسم المستخدم <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required
                                       pattern="[a-zA-Z0-9_]{3,20}" 
                                       title="3-20 حرف، أرقام وشرطة سفلية فقط">
                                <div class="form-text">3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope text-primary me-1"></i>
                                    البريد الإلكتروني <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label fw-semibold">
                                    <i class="bi bi-person-badge text-primary me-1"></i>
                                    الاسم الكامل <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="{{ user.full_name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label fw-semibold">
                                    <i class="bi bi-shield-check text-primary me-1"></i>
                                    الدور <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    {% for role_option in roles %}
                                    <option value="{{ role_option }}" 
                                            {% if user.role.value == role_option %}selected{% endif %}>
                                        {% if role_option == 'user' %}مستخدم عادي
                                        {% elif role_option == 'admin' %}مدير النظام
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="true" {% if user.is_active %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-person-check text-success me-1"></i>
                                        المستخدم نشط
                                    </label>
                                </div>
                                <div class="form-text">إلغاء التحديد لتعطيل المستخدم</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-calendar text-primary me-1"></i>
                                    تاريخ الإنشاء
                                </label>
                                <input type="text" class="form-control" value="{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}" disabled>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-end">
                                <a href="/admin/users" class="btn btn-outline-secondary px-4">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary px-4" id="submitBtn">
                                    <i class="bi bi-check-circle me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Password Reset -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-key text-warning me-2"></i>
                    إعادة تعيين كلمة المرور
                </h5>
                <p class="text-muted mb-0 small">تغيير كلمة مرور المستخدم</p>
            </div>
            <div class="card-body p-4">
                <form method="post" action="/admin/users/{{ user.id }}/reset-password" id="resetPasswordForm">
                    <div class="mb-3">
                        <label for="new_password" class="form-label fw-semibold">
                            <i class="bi bi-key text-primary me-1"></i>
                            كلمة المرور الجديدة <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password" required
                                   minlength="6" placeholder="أدخل كلمة مرور جديدة">
                            <button type="button" class="btn btn-outline-secondary" id="toggleNewPassword">
                                <i class="bi bi-eye" id="newPasswordIcon"></i>
                            </button>
                        </div>
                        <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_new_password" class="form-label fw-semibold">
                            <i class="bi bi-key-fill text-primary me-1"></i>
                            تأكيد كلمة المرور <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="confirm_new_password" required
                               minlength="6" placeholder="أعد إدخال كلمة المرور">
                        <div class="invalid-feedback" id="newPasswordMismatch">
                            كلمات المرور غير متطابقة
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning" id="resetBtn"
                                onclick="return confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')">
                            <i class="bi bi-key me-2"></i>
                            إعادة تعيين كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-graph-up text-info me-2"></i>
                    إحصائيات المستخدم
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">إجمالي الطلبات:</span>
                    <span class="fw-bold text-primary">{{ user.requests|length }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">الحالة:</span>
                    {% if user.is_active %}
                    <span class="badge bg-success">نشط</span>
                    {% else %}
                    <span class="badge bg-danger">معطل</span>
                    {% endif %}
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">الدور:</span>
                    {% if user.role.value == 'admin' %}
                    <span class="badge bg-warning">مدير النظام</span>
                    {% else %}
                    <span class="badge bg-info">مستخدم عادي</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const editForm = document.getElementById('editUserForm');
    const resetForm = document.getElementById('resetPasswordForm');
    const submitBtn = document.getElementById('submitBtn');
    const resetBtn = document.getElementById('resetBtn');
    
    const newPasswordField = document.getElementById('new_password');
    const confirmNewPasswordField = document.getElementById('confirm_new_password');
    const toggleNewPasswordBtn = document.getElementById('toggleNewPassword');
    const newPasswordIcon = document.getElementById('newPasswordIcon');

    // Toggle password visibility
    toggleNewPasswordBtn.addEventListener('click', function() {
        const type = newPasswordField.getAttribute('type') === 'password' ? 'text' : 'password';
        newPasswordField.setAttribute('type', type);
        newPasswordIcon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
    });

    // Password confirmation validation
    function validateNewPasswords() {
        const password = newPasswordField.value;
        const confirmPassword = confirmNewPasswordField.value;
        
        if (confirmPassword && password !== confirmPassword) {
            confirmNewPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
            confirmNewPasswordField.classList.add('is-invalid');
            return false;
        } else {
            confirmNewPasswordField.setCustomValidity('');
            confirmNewPasswordField.classList.remove('is-invalid');
            return true;
        }
    }

    confirmNewPasswordField.addEventListener('input', validateNewPasswords);
    newPasswordField.addEventListener('input', validateNewPasswords);

    // Form submission handling
    editForm.addEventListener('submit', function(e) {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    resetForm.addEventListener('submit', function(e) {
        if (!validateNewPasswords()) {
            e.preventDefault();
            return false;
        }
        
        resetBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري إعادة التعيين...';
        resetBtn.disabled = true;
    });

    // Username validation
    const usernameField = document.getElementById('username');
    usernameField.addEventListener('input', function() {
        const value = this.value;
        const pattern = /^[a-zA-Z0-9_]{3,20}$/;
        
        if (value && !pattern.test(value)) {
            this.setCustomValidity('اسم المستخدم يجب أن يحتوي على 3-20 حرف، أرقام وشرطة سفلية فقط');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
});
</script>
{% endblock %}
