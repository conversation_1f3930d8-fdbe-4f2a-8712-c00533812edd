{% extends "base.html" %}

{% block title %}تعديل الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-pencil-square"></i>
        تعديل الطلب
    </h2>
    <div>
        <a href="/requests/{{ req.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة لعرض الطلب
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1 fw-bold text-dark">
                            <i class="bi bi-pencil-square text-primary me-2"></i>
                            تعديل بيانات الطلب
                        </h4>
                        <p class="text-muted mb-0 small">رقم الطلب: <code>{{ req.request_number }}</code></p>
                    </div>
                    <div>
                        {% if req.status.value == 'pending' %}
                        <span class="badge bg-warning fs-6">قيد المراجعة</span>
                        {% elif req.status.value == 'in_progress' %}
                        <span class="badge bg-info fs-6">قيد التنفيذ</span>
                        {% elif req.status.value == 'completed' %}
                        <span class="badge bg-success fs-6">مكتمل</span>
                        {% elif req.status.value == 'rejected' %}
                        <span class="badge bg-danger fs-6">مرفوض</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="post" action="/requests/{{ req.id }}/edit" id="editRequestForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="request_name" class="form-label fw-semibold">
                                    <i class="bi bi-file-earmark text-primary me-1"></i>
                                    اسم الطلب <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="request_name" name="request_name" 
                                       value="{{ req.request_name }}" required
                                       placeholder="مثال: أرشفة وثائق المشروع">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="request_title" class="form-label fw-semibold">
                                    <i class="bi bi-card-heading text-primary me-1"></i>
                                    عنوان الطلب <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="request_title" name="request_title" 
                                       value="{{ req.request_title }}" required
                                       placeholder="مثال: أرشفة وثائق مشروع التطوير الجديد">
                            </div>
                        </div>
                    </div>

                    {% if current_user.role.value == 'admin' and statuses %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label fw-semibold">
                                    <i class="bi bi-flag text-primary me-1"></i>
                                    حالة الطلب
                                </label>
                                <select class="form-select" id="status" name="status">
                                    {% for status_option in statuses %}
                                    <option value="{{ status_option }}" 
                                            {% if req.status.value == status_option %}selected{% endif %}>
                                        {% if status_option == 'pending' %}قيد المراجعة
                                        {% elif status_option == 'in_progress' %}قيد التنفيذ
                                        {% elif status_option == 'completed' %}مكتمل
                                        {% elif status_option == 'rejected' %}مرفوض
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">يمكن للمديرين فقط تغيير حالة الطلب</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-person text-primary me-1"></i>
                                    مقدم الطلب
                                </label>
                                <input type="text" class="form-control" value="{{ req.user.full_name }}" disabled>
                                <div class="form-text">{{ req.user.email }}</div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label fw-semibold">
                                    <i class="bi bi-card-text text-primary me-1"></i>
                                    وصف الطلب
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="4"
                                          placeholder="اكتب وصفاً تفصيلياً للطلب...">{{ req.description or '' }}</textarea>
                                <div class="form-text">وصف تفصيلي لمحتوى الطلب ومتطلباته</div>
                            </div>
                        </div>
                    </div>

                    <!-- Request Info -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-calendar text-primary me-1"></i>
                                    تاريخ الإنشاء
                                </label>
                                <input type="text" class="form-control" value="{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}" disabled>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-clock text-primary me-1"></i>
                                    آخر تحديث
                                </label>
                                <input type="text" class="form-control" 
                                       value="{% if req.updated_at %}{{ req.updated_at.strftime('%Y-%m-%d %H:%M') }}{% else %}لم يتم التحديث{% endif %}" disabled>
                            </div>
                        </div>
                    </div>

                    <!-- Files Section -->
                    {% if req.files %}
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-paperclip text-primary me-1"></i>
                                    المرفقات الحالية ({{ req.files|length }})
                                </label>
                                <div class="border rounded p-3 bg-light">
                                    {% for file in req.files %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="bi bi-file-earmark text-muted me-2"></i>
                                            <span>{{ file.original_filename }}</span>
                                            <small class="text-muted ms-2">({{ (file.file_size / 1024 / 1024)|round(2) }} MB)</small>
                                        </div>
                                        <span class="badge bg-secondary">{{ file.file_type.upper() }}</span>
                                    </div>
                                    {% endfor %}
                                    <div class="form-text mt-2">
                                        <i class="bi bi-info-circle me-1"></i>
                                        لإدارة المرفقات (إضافة/حذف)،
                                        <a href="/requests/{{ req.id }}/files" class="text-decoration-none">
                                            استخدم صفحة إدارة الملفات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-end">
                                <a href="/requests/{{ req.id }}" class="btn btn-outline-secondary px-4">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary px-4" id="submitBtn">
                                    <i class="bi bi-check-circle me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editRequestForm');
    const submitBtn = document.getElementById('submitBtn');

    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // Auto-resize textarea
    const textarea = document.getElementById('description');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Trigger initial resize
    textarea.dispatchEvent(new Event('input'));
});
</script>
{% endblock %}
