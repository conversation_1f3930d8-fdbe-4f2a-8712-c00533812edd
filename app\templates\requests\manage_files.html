{% extends "base.html" %}

{% block title %}إدارة ملفات الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-files"></i>
        إدارة ملفات الطلب
    </h2>
    <div>
        <a href="/requests/{{ req.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للطلب
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Current Files -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 py-4">
                <h4 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-files text-primary me-2"></i>
                    الملفات الحالية ({{ req.files|length }}/5)
                </h4>
                <p class="text-muted mb-0 small">الملفات المرفقة بالطلب رقم: <code>{{ req.request_number }}</code></p>
            </div>
            <div class="card-body p-4">
                {% if req.files %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>النوع</th>
                                <th>الحجم</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in req.files %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark text-muted me-2"></i>
                                        <span>{{ file.original_filename }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ file.file_type.upper() }}</span>
                                </td>
                                <td>
                                    <span class="text-muted">{{ (file.file_size / 1024 / 1024)|round(2) }} MB</span>
                                </td>
                                <td>
                                    <span class="text-muted">{{ file.created_at.strftime('%Y-%m-%d %H:%M') if file.created_at else 'غير محدد' }}</span>
                                </td>
                                <td>
                                    <form method="post" action="/requests/{{ req.id }}/files/{{ file.id }}/delete" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                            <i class="bi bi-trash"></i>
                                            حذف
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-files display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">لا توجد ملفات مرفقة</h5>
                    <p class="text-muted">لم يتم إرفاق أي ملفات بهذا الطلب حتى الآن</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Add New Files -->
        {% if req.files|length < 5 %}
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-4">
                <h4 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-plus-circle text-success me-2"></i>
                    إضافة ملفات جديدة
                </h4>
                <p class="text-muted mb-0 small">يمكن إضافة {{ 5 - req.files|length }} ملف إضافي</p>
            </div>
            <div class="card-body p-4">
                <form method="post" action="/requests/{{ req.id }}/files/add" enctype="multipart/form-data" id="addFilesForm">
                    <div class="mb-3">
                        <label for="files" class="form-label fw-semibold">
                            <i class="bi bi-paperclip text-primary me-1"></i>
                            اختر الملفات <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="files" name="files" multiple required
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                        <div class="form-text">
                            الأنواع المسموحة: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF<br>
                            الحد الأقصى لحجم الملف: 10 ميجابايت<br>
                            العدد الأقصى للملفات: 5 ملفات لكل طلب
                        </div>
                    </div>

                    <div id="file-preview" class="mb-3"></div>

                    <div class="d-flex gap-3 justify-content-end">
                        <button type="button" class="btn btn-outline-secondary" onclick="document.getElementById('files').value=''; document.getElementById('file-preview').innerHTML='';">
                            <i class="bi bi-x-circle me-2"></i>
                            مسح الاختيار
                        </button>
                        <button type="submit" class="btn btn-success" id="addFilesBtn">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة الملفات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>
            تم الوصول للحد الأقصى من الملفات (5 ملفات). يجب حذف ملف موجود لإضافة ملف جديد.
        </div>
        {% endif %}
    </div>

    <div class="col-md-4">
        <!-- Request Info -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-info-circle text-info me-2"></i>
                    معلومات الطلب
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="mb-3">
                    <strong>رقم الطلب:</strong>
                    <div><code>{{ req.request_number }}</code></div>
                </div>
                <div class="mb-3">
                    <strong>عنوان الطلب:</strong>
                    <div>{{ req.request_title }}</div>
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <div>
                        {% if req.status.value == 'pending' %}
                        <span class="badge bg-warning">قيد المراجعة</span>
                        {% elif req.status.value == 'in_progress' %}
                        <span class="badge bg-info">قيد التنفيذ</span>
                        {% elif req.status.value == 'completed' %}
                        <span class="badge bg-success">مكتمل</span>
                        {% elif req.status.value == 'rejected' %}
                        <span class="badge bg-danger">مرفوض</span>
                        {% endif %}
                    </div>
                </div>
                <div class="mb-3">
                    <strong>مقدم الطلب:</strong>
                    <div>{{ req.user.full_name }}</div>
                </div>
                <div>
                    <strong>تاريخ الإنشاء:</strong>
                    <div>{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
            </div>
        </div>

        <!-- File Guidelines -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-lightbulb text-warning me-2"></i>
                    إرشادات الملفات
                </h5>
            </div>
            <div class="card-body p-4">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        الحد الأقصى: 5 ملفات لكل طلب
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        حجم الملف: حتى 10 ميجابايت
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        الأنواع المدعومة: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info me-2"></i>
                        يمكن حذف الملفات الموجودة
                    </li>
                    <li>
                        <i class="bi bi-info-circle text-info me-2"></i>
                        يمكن إضافة ملفات جديدة في أي وقت
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('files');
    const addFilesForm = document.getElementById('addFilesForm');
    const addFilesBtn = document.getElementById('addFilesBtn');

    // File preview
    function previewFiles(input) {
        const preview = document.getElementById('file-preview');
        preview.innerHTML = '';
        
        if (input.files) {
            Array.from(input.files).forEach(function(file, index) {
                const div = document.createElement('div');
                div.className = 'alert alert-info d-flex justify-content-between align-items-center';
                div.innerHTML = `
                    <span><i class="bi bi-file-earmark"></i> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                        <i class="bi bi-x"></i>
                    </button>
                `;
                preview.appendChild(div);
            });
        }
    }

    // File validation
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const files = this.files;
            const maxFiles = {{ 5 - req.files|length }};
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];
            
            if (files.length > maxFiles) {
                alert(`يمكنك رفع ${maxFiles} ملف كحد أقصى`);
                this.value = '';
                return;
            }
            
            for (let file of files) {
                const fileExt = file.name.split('.').pop().toLowerCase();
                
                if (!allowedTypes.includes(fileExt)) {
                    alert(`نوع الملف ${fileExt} غير مسموح`);
                    this.value = '';
                    return;
                }
                
                if (file.size > maxSize) {
                    alert(`حجم الملف ${file.name} يتجاوز الحد المسموح (10 ميجابايت)`);
                    this.value = '';
                    return;
                }
            }
            
            previewFiles(this);
        });
    }

    // Form submission
    if (addFilesForm) {
        addFilesForm.addEventListener('submit', function(e) {
            addFilesBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإضافة...';
            addFilesBtn.disabled = true;
        });
    }

    // Remove file function
    window.removeFile = function(index) {
        const input = document.getElementById('files');
        const dt = new DataTransfer();
        const files = input.files;
        
        for (let i = 0; i < files.length; i++) {
            if (i !== index) {
                dt.items.add(files[i]);
            }
        }
        
        input.files = dt.files;
        previewFiles(input);
    };
});
</script>
{% endblock %}
