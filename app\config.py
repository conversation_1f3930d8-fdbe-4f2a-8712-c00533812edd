from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://username:password@localhost:5432/cmsvs_db"
    
    # Security
    secret_key: str = "your-secret-key-here-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # File Upload
    max_file_size: int = 10485760  # 10MB
    allowed_file_types: str = "pdf,doc,docx,txt,jpg,jpeg,png,gif"
    upload_directory: str = "uploads"
    
    # Application
    app_name: str = "CMSVS Internal System"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # Admin
    admin_email: str = "<EMAIL>"
    admin_password: str = "admin123"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    @property
    def allowed_file_types_list(self) -> list[str]:
        return [ext.strip().lower() for ext in self.allowed_file_types.split(",")]


# Create settings instance
settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.upload_directory, exist_ok=True)
