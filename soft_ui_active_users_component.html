<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soft UI Active Users Component - CMSVS</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --soft-bg: #f8f9fa;
            --soft-white: #ffffff;
            --soft-primary: #5e72e4;
            --soft-secondary: #8392ab;
            --soft-success: #2dce89;
            --soft-info: #11cdef;
            --soft-warning: #fb6340;
            --soft-danger: #f5365c;
            --soft-dark: #32325d;
            --soft-light: #e9ecef;
            --soft-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12), 0 0.125rem 0.25rem -0.0625rem rgba(20, 20, 20, 0.07);
            --soft-shadow-lg: 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15), 0 0.25rem 0.5rem -0.125rem rgba(20, 20, 20, 0.1);
            --soft-border-radius: 0.75rem;
            --soft-border-radius-lg: 1rem;
            --soft-border-radius-xl: 1.5rem;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--soft-dark);
            padding: 2rem;
        }

        /* Soft UI Active Users Component */
        .active-users-card {
            background: var(--soft-white);
            border-radius: var(--soft-border-radius-xl);
            box-shadow: var(--soft-shadow);
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
            position: relative;
        }

        .active-users-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--soft-shadow-lg);
        }

        .active-users-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--soft-success) 0%, var(--soft-info) 100%);
            border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
        }

        .active-users-header {
            padding: 1.5rem 2rem 1rem 2rem;
            background: linear-gradient(135deg, rgba(45, 206, 137, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
            position: relative;
        }

        .active-users-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .active-users-title h5 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--soft-dark);
            display: flex;
            align-items: center;
        }

        .active-users-title .bi {
            color: var(--soft-success);
            margin-left: 0.5rem;
            font-size: 1.25rem;
        }

        .active-users-badge {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            box-shadow: 0 0.125rem 0.25rem rgba(45, 206, 137, 0.3);
        }

        .active-users-badge .bi {
            margin-left: 0.25rem;
            font-size: 0.875rem;
        }

        .active-users-main {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .active-users-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--soft-dark);
            line-height: 1;
            background: linear-gradient(135deg, var(--soft-success) 0%, var(--soft-info) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .active-users-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            border-radius: var(--soft-border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(45, 206, 137, 0.3);
            position: relative;
            overflow: hidden;
        }

        .active-users-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .active-users-card:hover .active-users-icon::before {
            opacity: 1;
            animation: shimmer 1.5s ease-in-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .active-users-body {
            padding: 0 2rem 2rem 2rem;
        }

        .active-users-subtitle {
            color: var(--soft-secondary);
            font-size: 0.875rem;
            margin: 0.5rem 0 1.5rem 0;
            font-weight: 500;
        }

        .active-users-stats {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
        }

        .active-users-stat {
            flex: 1;
            text-align: center;
            padding: 1rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);
            border-radius: var(--soft-border-radius);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .active-users-stat:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
            background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,249,250,1) 100%);
        }

        .active-users-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--soft-dark);
            margin-bottom: 0.25rem;
        }

        .active-users-stat-label {
            font-size: 0.75rem;
            color: var(--soft-secondary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .active-users-progress {
            margin-top: 1.5rem;
        }

        .active-users-progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--soft-dark);
        }

        .active-users-progress-bar {
            height: 8px;
            background: var(--soft-light);
            border-radius: 50px;
            overflow: hidden;
            position: relative;
        }

        .active-users-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--soft-success) 0%, var(--soft-info) 100%);
            border-radius: 50px;
            width: 0%;
            transition: width 1.5s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .active-users-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 2s ease-in-out infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .active-users-header {
                padding: 1rem 1.5rem 0.75rem 1.5rem;
            }

            .active-users-body {
                padding: 0 1.5rem 1.5rem 1.5rem;
            }

            .active-users-main {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .active-users-number {
                font-size: 2rem;
            }

            .active-users-icon {
                width: 3rem;
                height: 3rem;
                font-size: 1.5rem;
            }

            .active-users-stats {
                flex-direction: column;
                gap: 0.75rem;
            }

            .active-users-title {
                flex-direction: column;
                gap: 0.75rem;
                align-items: flex-start;
            }
        }

        /* Animation for component appearance */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .active-users-card {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Pulse animation for live indicator */
        .pulse-dot {
            width: 8px;
            height: 8px;
            background: var(--soft-success);
            border-radius: 50%;
            margin-left: 0.5rem;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(45, 206, 137, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(45, 206, 137, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(45, 206, 137, 0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-5">👥 Soft UI Active Users Component</h1>
        
        <!-- Active Users Component -->
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="active-users-card">
                    <div class="active-users-header">
                        <div class="active-users-title">
                            <h5>
                                <i class="bi bi-people-fill"></i>
                                المستخدمون النشطون
                            </h5>
                            <div class="active-users-badge">
                                <i class="bi bi-circle-fill pulse-dot"></i>
                                مباشر
                            </div>
                        </div>
                        
                        <div class="active-users-main">
                            <div class="active-users-number" id="activeUsersCount">2,340</div>
                            <div class="active-users-icon">
                                <i class="bi bi-person-check-fill"></i>
                            </div>
                        </div>
                        
                        <p class="active-users-subtitle">
                            <i class="bi bi-arrow-up text-success me-1"></i>
                            +12% من الأسبوع الماضي
                        </p>
                    </div>
                    
                    <div class="active-users-body">
                        <div class="active-users-stats">
                            <div class="active-users-stat">
                                <div class="active-users-stat-number">1,890</div>
                                <div class="active-users-stat-label">اليوم</div>
                            </div>
                            <div class="active-users-stat">
                                <div class="active-users-stat-number">12,450</div>
                                <div class="active-users-stat-label">هذا الأسبوع</div>
                            </div>
                            <div class="active-users-stat">
                                <div class="active-users-stat-number">48,200</div>
                                <div class="active-users-stat-label">هذا الشهر</div>
                            </div>
                        </div>
                        
                        <div class="active-users-progress">
                            <div class="active-users-progress-label">
                                <span>معدل النشاط</span>
                                <span class="text-success fw-bold">78%</span>
                            </div>
                            <div class="active-users-progress-bar">
                                <div class="active-users-progress-fill" id="activityProgress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Examples -->
        <div class="row mt-5">
            <div class="col-md-4">
                <div class="active-users-card">
                    <div class="active-users-header">
                        <div class="active-users-title">
                            <h5>
                                <i class="bi bi-file-earmark-text"></i>
                                الطلبات الجديدة
                            </h5>
                            <div class="active-users-badge" style="background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);">
                                <i class="bi bi-circle-fill pulse-dot" style="background: var(--soft-primary);"></i>
                                جديد
                            </div>
                        </div>
                        
                        <div class="active-users-main">
                            <div class="active-users-number" style="background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">156</div>
                            <div class="active-users-icon" style="background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%); box-shadow: 0 0.25rem 0.5rem rgba(94, 114, 228, 0.3);">
                                <i class="bi bi-plus-circle-fill"></i>
                            </div>
                        </div>
                        
                        <p class="active-users-subtitle">
                            <i class="bi bi-arrow-up text-primary me-1"></i>
                            +8% من أمس
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="active-users-card">
                    <div class="active-users-header">
                        <div class="active-users-title">
                            <h5>
                                <i class="bi bi-check-circle"></i>
                                المهام المكتملة
                            </h5>
                            <div class="active-users-badge" style="background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);">
                                <i class="bi bi-circle-fill pulse-dot" style="background: var(--soft-info);"></i>
                                محدث
                            </div>
                        </div>
                        
                        <div class="active-users-main">
                            <div class="active-users-number" style="background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">89</div>
                            <div class="active-users-icon" style="background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%); box-shadow: 0 0.25rem 0.5rem rgba(17, 205, 239, 0.3);">
                                <i class="bi bi-check-all"></i>
                            </div>
                        </div>
                        
                        <p class="active-users-subtitle">
                            <i class="bi bi-arrow-up text-info me-1"></i>
                            +15% من الأمس
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="active-users-card">
                    <div class="active-users-header">
                        <div class="active-users-title">
                            <h5>
                                <i class="bi bi-exclamation-triangle"></i>
                                تحتاج مراجعة
                            </h5>
                            <div class="active-users-badge" style="background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);">
                                <i class="bi bi-circle-fill pulse-dot" style="background: var(--soft-warning);"></i>
                                عاجل
                            </div>
                        </div>
                        
                        <div class="active-users-main">
                            <div class="active-users-number" style="background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">23</div>
                            <div class="active-users-icon" style="background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%); box-shadow: 0 0.25rem 0.5rem rgba(251, 99, 64, 0.3);">
                                <i class="bi bi-clock-fill"></i>
                            </div>
                        </div>
                        
                        <p class="active-users-subtitle">
                            <i class="bi bi-arrow-down text-warning me-1"></i>
                            -3% من أمس
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animate progress bar and numbers on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bar
            setTimeout(() => {
                document.getElementById('activityProgress').style.width = '78%';
            }, 500);
            
            // Animate counter
            animateCounter('activeUsersCount', 2340, 2000);
        });
        
        function animateCounter(elementId, targetValue, duration) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const increment = targetValue / (duration / 16);
            let currentValue = startValue;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue).toLocaleString('ar-SA');
            }, 16);
        }
    </script>
</body>
</html>
