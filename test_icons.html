<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap Icons Test</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons with fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous" onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css';">

    <!-- Font Awesome as fallback -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem;
        }
        
        .test-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12);
        }
        
        .icon-test {
            font-size: 2rem;
            margin: 0.5rem;
            padding: 1rem;
            background: #5e72e4;
            color: white;
            border-radius: 0.5rem;
            display: inline-block;
        }
        
        .avatar-lg {
            width: 4rem;
            height: 4rem;
            background: #5e72e4;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
        }

        /* Enhanced Icon Support */
        .bi {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            vertical-align: -.125em !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: inline-block !important;
            position: relative !important;
            z-index: 10 !important;
        }

        /* RTL Support */
        [dir="rtl"] .bi {
            direction: ltr !important;
            unicode-bidi: bidi-override !important;
            transform: scaleX(-1) !important;
        }

        .bg-gradient .bi {
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1)) !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2)) !important;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-loaded { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-5">Bootstrap Icons Test</h1>
        
        <!-- Basic Icon Test -->
        <div class="test-card">
            <h3>Basic Icons Test</h3>
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                    <p>bi-file-earmark-text</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-clock"></i>
                    </div>
                    <p>bi-clock</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-gear"></i>
                    </div>
                    <p>bi-gear</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <p>bi-check-circle</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-people"></i>
                    </div>
                    <p>bi-people</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <p>bi-person-check</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <p>bi-shield-check</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="icon-test">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <p>bi-speedometer2</p>
                </div>
            </div>
        </div>
        
        <!-- Avatar Style Test (like in dashboard) -->
        <div class="test-card">
            <h3>Avatar Style Test (Dashboard Style)</h3>
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="avatar-lg bg-primary bg-gradient mx-auto mb-3">
                        <i class="bi bi-file-earmark-text fs-3 text-white"></i>
                    </div>
                    <p>Dashboard Style Icon</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="avatar-lg bg-warning bg-gradient mx-auto mb-3">
                        <i class="bi bi-clock fs-3 text-white"></i>
                    </div>
                    <p>Dashboard Style Icon</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="avatar-lg bg-success bg-gradient mx-auto mb-3">
                        <i class="bi bi-check-circle fs-3 text-white"></i>
                    </div>
                    <p>Dashboard Style Icon</p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="avatar-lg bg-info bg-gradient mx-auto mb-3">
                        <i class="bi bi-people fs-3 text-white"></i>
                    </div>
                    <p>Dashboard Style Icon</p>
                </div>
            </div>
        </div>
        
        <!-- CSS Debug Info -->
        <div class="test-card">
            <h3>CSS Debug Information</h3>
            <p><strong>Bootstrap Icons CDN:</strong> https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css</p>
            <p><strong>Bootstrap RTL CDN:</strong> https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css</p>
            <p><strong>Font Family Check:</strong> <span style="font-family: 'bootstrap-icons';">This text should use bootstrap-icons font</span></p>
            <p><strong>Icon Loading Status:</strong> <span id="icon-status">Checking...</span> <span id="status-indicator" class="status-indicator status-loading"></span></p>
            <p><strong>Font Detection:</strong> <span id="font-detection">Testing...</span></p>
        </div>

        <!-- Real-time Icon Test -->
        <div class="test-card">
            <h3>Real-time Icon Test</h3>
            <div class="row" id="dynamic-icons">
                <!-- Icons will be added here dynamically -->
            </div>
            <button class="btn btn-primary mt-3" onclick="testAllIcons()">Test All Dashboard Icons</button>
            <button class="btn btn-secondary mt-3" onclick="refreshIcons()">Refresh Icons</button>
        </div>
    </div>

    <script>
        // Enhanced Icon Testing System
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardIcons = [
                'bi-speedometer2', 'bi-person', 'bi-people', 'bi-file-earmark-text',
                'bi-clock', 'bi-gear', 'bi-check-circle', 'bi-arrow-up',
                'bi-arrow-left', 'bi-eye', 'bi-shield-check', 'bi-shield',
                'bi-person-check', 'bi-play-circle', 'bi-x-circle'
            ];

            function checkBootstrapIcons() {
                const testElement = document.createElement('i');
                testElement.className = 'bi bi-house';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);

                const computedStyle = window.getComputedStyle(testElement);
                const fontFamily = computedStyle.getPropertyValue('font-family');

                document.body.removeChild(testElement);

                return fontFamily.includes('bootstrap-icons');
            }

            function updateStatus() {
                const statusElement = document.getElementById('icon-status');
                const indicatorElement = document.getElementById('status-indicator');
                const fontDetectionElement = document.getElementById('font-detection');

                if (checkBootstrapIcons()) {
                    statusElement.textContent = 'Bootstrap Icons Loaded Successfully ✓';
                    indicatorElement.className = 'status-indicator status-loaded';
                    fontDetectionElement.textContent = 'Bootstrap Icons font detected';
                } else {
                    statusElement.textContent = 'Bootstrap Icons Failed to Load ✗';
                    indicatorElement.className = 'status-indicator status-failed';
                    fontDetectionElement.textContent = 'Bootstrap Icons font NOT detected';
                }
            }

            window.testAllIcons = function() {
                const container = document.getElementById('dynamic-icons');
                container.innerHTML = '';

                dashboardIcons.forEach(iconClass => {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 text-center mb-3';

                    const iconDiv = document.createElement('div');
                    iconDiv.className = 'icon-test';

                    const icon = document.createElement('i');
                    icon.className = `bi ${iconClass}`;

                    const label = document.createElement('p');
                    label.textContent = iconClass;
                    label.className = 'mt-2 small';

                    iconDiv.appendChild(icon);
                    col.appendChild(iconDiv);
                    col.appendChild(label);
                    container.appendChild(col);
                });
            };

            window.refreshIcons = function() {
                updateStatus();
                const icons = document.querySelectorAll('.bi');
                icons.forEach(icon => {
                    icon.style.fontFamily = '"bootstrap-icons", "Font Awesome 6 Free", system-ui, sans-serif';
                });
            };

            // Initial status check
            updateStatus();

            // Auto-refresh every 3 seconds
            setInterval(updateStatus, 3000);
        });
    </script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
