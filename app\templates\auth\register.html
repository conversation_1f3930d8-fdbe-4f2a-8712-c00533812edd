{% extends "base.html" %}

{% block title %}إنشاء حساب جديد - CMSVS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="bi bi-person-plus display-4 text-success"></i>
                    <h3 class="card-title mt-2">إنشاء حساب جديد</h3>
                    <p class="text-muted">انضم إلى نظام الأرشفة الداخلي</p>
                </div>

                <form method="post" action="/register">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" required 
                                   pattern="[a-zA-Z0-9_]{3,20}" title="3-20 حرف، أرقام وشرطة سفلية فقط">
                        </div>
                        <div class="form-text">3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person-badge"></i>
                            </span>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required 
                                   minlength="6">
                        </div>
                        <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock-fill"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-person-plus"></i>
                            إنشاء الحساب
                        </button>
                    </div>
                </form>

                <hr>
                
                <div class="text-center">
                    <p class="mb-0">لديك حساب بالفعل؟</p>
                    <a href="/login" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-box-arrow-in-right"></i>
                        تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    var password = document.getElementById('password').value;
    var confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    var confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});
</script>
{% endblock %}
