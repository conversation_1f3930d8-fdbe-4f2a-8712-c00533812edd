<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soft UI Chart Component - CMSVS</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --soft-bg: #f8f9fa;
            --soft-white: #ffffff;
            --soft-primary: #5e72e4;
            --soft-secondary: #8392ab;
            --soft-success: #2dce89;
            --soft-info: #11cdef;
            --soft-warning: #fb6340;
            --soft-danger: #f5365c;
            --soft-dark: #32325d;
            --soft-light: #e9ecef;
            --soft-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12), 0 0.125rem 0.25rem -0.0625rem rgba(20, 20, 20, 0.07);
            --soft-shadow-lg: 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15), 0 0.25rem 0.5rem -0.125rem rgba(20, 20, 20, 0.1);
            --soft-border-radius: 0.75rem;
            --soft-border-radius-lg: 1rem;
            --soft-border-radius-xl: 1.5rem;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--soft-dark);
            padding: 2rem;
        }

        /* Soft UI Chart Component */
        .chart-container {
            background: var(--soft-white);
            border-radius: var(--soft-border-radius-xl);
            box-shadow: var(--soft-shadow);
            padding: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
        }

        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: var(--soft-shadow-lg);
        }

        .chart-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            position: relative;
        }

        .chart-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--soft-primary) 0%, var(--soft-info) 50%, var(--soft-success) 100%);
            border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--soft-dark);
            margin: 0;
            display: flex;
            align-items: center;
        }

        .chart-title .bi {
            color: var(--soft-primary);
            margin-left: 0.75rem;
            font-size: 1.5rem;
        }

        .chart-subtitle {
            color: var(--soft-secondary);
            font-size: 0.875rem;
            margin: 0.5rem 0 0 0;
            font-weight: 500;
        }

        .chart-body {
            padding: 2rem;
            position: relative;
        }

        .chart-canvas-wrapper {
            position: relative;
            height: 300px;
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
            border-radius: var(--soft-border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .chart-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .chart-stat-item {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);
            padding: 0.75rem 1rem;
            border-radius: var(--soft-border-radius);
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .chart-stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
        }

        .chart-stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1rem;
        }

        .chart-stat-icon.primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
        }

        .chart-stat-icon.success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            color: white;
        }

        .chart-stat-icon.info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
            color: white;
        }

        .chart-stat-content h6 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--soft-dark);
        }

        .chart-stat-content small {
            color: var(--soft-secondary);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .chart-legend-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: var(--soft-secondary);
            font-weight: 500;
        }

        .chart-legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }

        .chart-legend-color.primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
        }

        .chart-legend-color.success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
        }

        .chart-legend-color.info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .chart-header {
                padding: 1rem 1.5rem;
            }

            .chart-body {
                padding: 1.5rem;
            }

            .chart-canvas-wrapper {
                height: 250px;
                padding: 0.75rem;
            }

            .chart-stats {
                flex-direction: column;
                align-items: stretch;
            }

            .chart-stat-item {
                justify-content: center;
            }

            .chart-legend {
                gap: 1rem;
            }

            .chart-title {
                font-size: 1.1rem;
            }
        }

        /* Animation for chart appearance */
        @keyframes chartFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chart-container {
            animation: chartFadeIn 0.6s ease-out;
        }

        /* Custom scrollbar for overflow content */
        .chart-container::-webkit-scrollbar {
            width: 6px;
        }

        .chart-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chart-container::-webkit-scrollbar-thumb {
            background: var(--soft-primary);
            border-radius: 3px;
        }

        .chart-container::-webkit-scrollbar-thumb:hover {
            background: #4c63d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-5">📊 Soft UI Chart Component</h1>
        
        <!-- Main Chart Component -->
        <div class="row">
            <div class="col-12">
                <div class="chart-container">
                    <div class="chart-header">
                        <h4 class="chart-title">
                            <i class="bi bi-graph-up"></i>
                            نظرة عامة على المبيعات
                        </h4>
                        <p class="chart-subtitle">
                            <i class="bi bi-arrow-up text-success me-1"></i>
                            (+5%) أكثر في عام 2023
                        </p>
                    </div>
                    
                    <div class="chart-body">
                        <div class="chart-canvas-wrapper">
                            <canvas id="salesChart"></canvas>
                        </div>
                        
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-content">
                                    <h6>281</h6>
                                    <small>إجمالي الطلبات</small>
                                </div>
                                <div class="chart-stat-icon primary">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                            </div>
                            
                            <div class="chart-stat-item">
                                <div class="chart-stat-content">
                                    <h6>2,300</h6>
                                    <small>إجمالي المستخدمين</small>
                                </div>
                                <div class="chart-stat-icon success">
                                    <i class="bi bi-people"></i>
                                </div>
                            </div>
                            
                            <div class="chart-stat-item">
                                <div class="chart-stat-content">
                                    <h6>940</h6>
                                    <small>الملفات المعالجة</small>
                                </div>
                                <div class="chart-stat-icon info">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chart-legend">
                            <div class="chart-legend-item">
                                <span class="chart-legend-color primary"></span>
                                الطلبات الجديدة
                            </div>
                            <div class="chart-legend-item">
                                <span class="chart-legend-color success"></span>
                                الطلبات المكتملة
                            </div>
                            <div class="chart-legend-item">
                                <span class="chart-legend-color info"></span>
                                قيد المراجعة
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Chart with Soft UI styling
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            
            // Create gradient for the chart
            const gradient = ctx.createLinearGradient(0, 0, 0, 300);
            gradient.addColorStop(0, 'rgba(94, 114, 228, 0.3)');
            gradient.addColorStop(0.5, 'rgba(17, 205, 239, 0.2)');
            gradient.addColorStop(1, 'rgba(45, 206, 137, 0.1)');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                    datasets: [{
                        label: 'الطلبات',
                        data: [12, 19, 3, 5, 2, 3, 20],
                        borderColor: '#5e72e4',
                        backgroundColor: gradient,
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#5e72e4',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#8392ab',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#8392ab',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: '#5e72e4'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
