<!-- Soft UI Chart Component Integration for CMSVS Dashboard -->
<!-- Add this to your existing dashboard template -->

<!-- Chart Component CSS (Add to your base.html <style> section) -->
<style>
/* Soft UI Chart Component Styles */
.soft-chart-container {
    background: var(--soft-white);
    border-radius: var(--soft-border-radius-xl);
    box-shadow: var(--soft-shadow);
    padding: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
}

.soft-chart-container:hover {
    transform: translateY(-5px);
    box-shadow: var(--soft-shadow-lg);
}

.soft-chart-header {
    background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.soft-chart-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--soft-primary) 0%, var(--soft-info) 50%, var(--soft-success) 100%);
    border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
}

.soft-chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--soft-dark);
    margin: 0;
    display: flex;
    align-items: center;
}

.soft-chart-title .bi {
    color: var(--soft-primary);
    margin-left: 0.75rem;
    font-size: 1.5rem;
}

.soft-chart-subtitle {
    color: var(--soft-secondary);
    font-size: 0.875rem;
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

.soft-chart-body {
    padding: 2rem;
    position: relative;
}

.soft-chart-canvas-wrapper {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, rgba(94, 114, 228, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
    border-radius: var(--soft-border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Active Users Component Styles */
.soft-active-users-card {
    background: var(--soft-white);
    border-radius: var(--soft-border-radius-xl);
    box-shadow: var(--soft-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    position: relative;
}

.soft-active-users-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--soft-shadow-lg);
}

.soft-active-users-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--soft-success) 0%, var(--soft-info) 100%);
    border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
}

.soft-active-users-header {
    padding: 1.5rem 2rem 1rem 2rem;
    background: linear-gradient(135deg, rgba(45, 206, 137, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
}

.soft-active-users-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.soft-active-users-title h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--soft-dark);
    display: flex;
    align-items: center;
}

.soft-active-users-badge {
    background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 0.125rem 0.25rem rgba(45, 206, 137, 0.3);
}

.soft-active-users-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.soft-active-users-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--soft-dark);
    line-height: 1;
    background: linear-gradient(135deg, var(--soft-success) 0%, var(--soft-info) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.soft-active-users-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
    border-radius: var(--soft-border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 0.25rem 0.5rem rgba(45, 206, 137, 0.3);
}

.soft-active-users-body {
    padding: 0 2rem 2rem 2rem;
}

.soft-active-users-subtitle {
    color: var(--soft-secondary);
    font-size: 0.875rem;
    margin: 0.5rem 0 1.5rem 0;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .soft-chart-header,
    .soft-active-users-header {
        padding: 1rem 1.5rem;
    }
    
    .soft-chart-body,
    .soft-active-users-body {
        padding: 1.5rem;
    }
    
    .soft-chart-canvas-wrapper {
        height: 250px;
    }
    
    .soft-active-users-main {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .soft-active-users-number {
        font-size: 2rem;
    }
}

/* Animation */
@keyframes softFadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.soft-chart-container,
.soft-active-users-card {
    animation: softFadeInUp 0.6s ease-out;
}
</style>

<!-- Chart Component HTML -->
<div class="col-lg-8">
    <div class="soft-chart-container">
        <div class="soft-chart-header">
            <h4 class="soft-chart-title">
                <i class="bi bi-graph-up"></i>
                نظرة عامة على الطلبات
            </h4>
            <p class="soft-chart-subtitle">
                <i class="bi bi-arrow-up text-success me-1"></i>
                (+{{ ((request_stats.total - request_stats.last_month) / request_stats.last_month * 100)|round(1) }}%) أكثر من الشهر الماضي
            </p>
        </div>
        
        <div class="soft-chart-body">
            <div class="soft-chart-canvas-wrapper">
                <canvas id="requestsChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Active Users Component HTML -->
<div class="col-lg-4">
    <div class="soft-active-users-card">
        <div class="soft-active-users-header">
            <div class="soft-active-users-title">
                <h5>
                    <i class="bi bi-people-fill"></i>
                    المستخدمون النشطون
                </h5>
                <div class="soft-active-users-badge">
                    <i class="bi bi-circle-fill" style="width: 8px; height: 8px; margin-left: 0.25rem;"></i>
                    مباشر
                </div>
            </div>
            
            <div class="soft-active-users-main">
                <div class="soft-active-users-number">{{ user_stats.active }}</div>
                <div class="soft-active-users-icon">
                    <i class="bi bi-person-check-fill"></i>
                </div>
            </div>
            
            <p class="soft-active-users-subtitle">
                <i class="bi bi-arrow-up text-success me-1"></i>
                +{{ ((user_stats.active - user_stats.last_week_active) / user_stats.last_week_active * 100)|round(1) }}% من الأسبوع الماضي
            </p>
        </div>
        
        <div class="soft-active-users-body">
            <div class="row text-center">
                <div class="col-4">
                    <div class="p-2">
                        <h6 class="fw-bold text-dark mb-1">{{ user_stats.today }}</h6>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-2">
                        <h6 class="fw-bold text-dark mb-1">{{ user_stats.this_week }}</h6>
                        <small class="text-muted">هذا الأسبوع</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-2">
                        <h6 class="fw-bold text-dark mb-1">{{ user_stats.this_month }}</h6>
                        <small class="text-muted">هذا الشهر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Chart Integration -->
<script>
// Add this to your existing JavaScript section
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Requests Chart
    const ctx = document.getElementById('requestsChart');
    if (ctx) {
        const chartCtx = ctx.getContext('2d');
        
        // Create gradient
        const gradient = chartCtx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(94, 114, 228, 0.3)');
        gradient.addColorStop(0.5, 'rgba(17, 205, 239, 0.2)');
        gradient.addColorStop(1, 'rgba(45, 206, 137, 0.1)');
        
        new Chart(chartCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                datasets: [{
                    label: 'الطلبات',
                    data: [
                        {{ monthly_stats.jan|default(0) }},
                        {{ monthly_stats.feb|default(0) }},
                        {{ monthly_stats.mar|default(0) }},
                        {{ monthly_stats.apr|default(0) }},
                        {{ monthly_stats.may|default(0) }},
                        {{ monthly_stats.jun|default(0) }},
                        {{ monthly_stats.jul|default(0) }}
                    ],
                    borderColor: '#5e72e4',
                    backgroundColor: gradient,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#5e72e4',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#8392ab',
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#8392ab',
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
