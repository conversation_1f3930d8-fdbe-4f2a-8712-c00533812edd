{% extends "base.html" %}

{% block title %}تم إنشاء الطلب بنجاح - CMSVS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="bi bi-check-circle"></i>
                    تم إنشاء الطلب بنجاح
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h5 class="alert-heading">
                        <i class="bi bi-check-circle-fill"></i>
                        تم إرسال طلبك بنجاح!
                    </h5>
                    <p class="mb-0">سيتم مراجعة طلبك من قبل الإدارة وستحصل على إشعار بحالة الطلب.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل الطلب:</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الطلب:</strong></td>
                                <td><code>{{ new_request.request_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>الرمز التعريفي:</strong></td>
                                <td><code>{{ new_request.unique_code }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>اسم الطلب:</strong></td>
                                <td>{{ new_request.request_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>{{ new_request.request_title }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td><span class="badge bg-warning">قيد المراجعة</span></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ new_request.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if new_request.files %}
                        <h6>المرفقات:</h6>
                        <ul class="list-group">
                            {% for file in new_request.files %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark"></i>
                                    {{ file.original_filename }}
                                </div>
                                <span class="badge bg-secondary">{{ file.file_size_mb }} MB</span>
                            </li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <div class="text-muted">
                            <i class="bi bi-file-earmark-x"></i>
                            لا توجد مرفقات
                        </div>
                        {% endif %}
                    </div>
                </div>

                {% if new_request.description %}
                <div class="mt-3">
                    <h6>الوصف:</h6>
                    <p class="text-muted">{{ new_request.description }}</p>
                </div>
                {% endif %}

                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading">
                        <i class="bi bi-lightbulb"></i>
                        نصائح مهمة:
                    </h6>
                    <ul class="mb-0">
                        <li>احتفظ برقم الطلب <code>{{ new_request.request_number }}</code> للمراجعة المستقبلية</li>
                        <li>يمكنك متابعة حالة الطلب من لوحة التحكم الخاصة بك</li>
                        <li>ستحصل على إشعار عند تغيير حالة الطلب</li>
                    </ul>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="/dashboard" class="btn btn-primary">
                        <i class="bi bi-house"></i>
                        العودة للرئيسية
                    </a>
                    <div>
                        <a href="/requests/{{ new_request.id }}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-eye"></i>
                            عرض الطلب
                        </a>
                        <a href="/requests/new" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i>
                            طلب جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
