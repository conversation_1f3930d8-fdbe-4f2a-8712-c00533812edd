version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: cmsvs_db
    environment:
      POSTGRES_DB: cmsvs_db
      POSTGRES_USER: cmsvs_user
      POSTGRES_PASSWORD: cmsvs_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - cmsvs_network
    restart: unless-stopped

  # CMSVS Application
  app:
    build: .
    container_name: cmsvs_app
    environment:
      DATABASE_URL: **********************************************/cmsvs_db
      SECRET_KEY: your-secret-key-change-this-in-production
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: admin123
      DEBUG: "False"
    volumes:
      - ./uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - db
    networks:
      - cmsvs_network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  cmsvs_network:
    driver: bridge
