<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Color Test - CMSVS Dashboard Style</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons with fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous" onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css';">
    
    <style>
        :root {
            --soft-bg: #f8f9fa;
            --soft-white: #ffffff;
            --soft-primary: #5e72e4;
            --soft-secondary: #8392ab;
            --soft-success: #2dce89;
            --soft-info: #11cdef;
            --soft-warning: #fb6340;
            --soft-danger: #f5365c;
            --soft-dark: #32325d;
            --soft-light: #e9ecef;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--soft-dark);
            padding: 2rem;
        }

        .test-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12);
        }

        /* Apply the same icon color fixes as in the main dashboard */
        .bi {
            font-family: "bootstrap-icons" !important;
            color: var(--soft-dark) !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Navigation bar icons - white on dark background */
        .navbar .bi,
        .navbar-brand .bi,
        .nav-link .bi {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        /* Sidebar icons - dark on light background */
        .sidebar .bi,
        .list-group-item .bi {
            color: var(--soft-secondary) !important;
        }

        .list-group-item:hover .bi,
        .list-group-item.active .bi {
            color: white !important;
        }

        /* Button icons */
        .btn-primary .bi,
        .btn-success .bi,
        .btn-info .bi,
        .btn-warning .bi,
        .btn-danger .bi {
            color: white !important;
        }

        .btn-outline-primary .bi {
            color: var(--soft-primary) !important;
        }

        /* Statistics card icons */
        .card-stats .bi,
        .avatar-lg .bi,
        .avatar-sm .bi {
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
        }

        /* Card header icons */
        .card-header .bi {
            color: var(--soft-dark) !important;
        }

        /* Utility classes */
        .text-primary .bi { color: var(--soft-primary) !important; }
        .text-secondary .bi { color: var(--soft-secondary) !important; }
        .text-success .bi { color: var(--soft-success) !important; }
        .text-info .bi { color: var(--soft-info) !important; }
        .text-warning .bi { color: var(--soft-warning) !important; }
        .text-danger .bi { color: var(--soft-danger) !important; }
        .text-dark .bi { color: var(--soft-dark) !important; }
        .text-muted .bi { color: #6c757d !important; }
        .text-white .bi { color: white !important; }

        /* Test specific styles */
        .navbar-test {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .sidebar-test {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .list-group-item {
            border: none;
            background: transparent;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.75rem;
            transition: all 0.3s ease;
            color: var(--soft-secondary);
        }

        .list-group-item:hover,
        .list-group-item.active {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
        }

        .avatar-lg {
            width: 4rem;
            height: 4rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
        }

        .avatar-sm {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-stats {
            border: none;
            border-radius: 1rem;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-5">🎨 Icon Color Contrast Test</h1>
        
        <!-- Navigation Test -->
        <div class="test-section">
            <h3>Navigation Bar Test</h3>
            <div class="navbar-test">
                <div class="d-flex align-items-center">
                    <i class="bi bi-archive fs-4 me-3"></i>
                    <span class="fw-bold me-4">CMSVS</span>
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    <i class="bi bi-people ms-3 me-2"></i>Users
                    <i class="bi bi-file-earmark-text ms-3 me-2"></i>Files
                </div>
            </div>
            <p><strong>Expected:</strong> All icons should be white and clearly visible on the blue gradient background.</p>
        </div>

        <!-- Sidebar Test -->
        <div class="test-section">
            <h3>Sidebar Navigation Test</h3>
            <div class="sidebar-test">
                <div class="list-group">
                    <div class="list-group-item">
                        <i class="bi bi-house me-2"></i>Home
                    </div>
                    <div class="list-group-item">
                        <i class="bi bi-gear me-2"></i>Settings
                    </div>
                    <div class="list-group-item active">
                        <i class="bi bi-activity me-2"></i>Active Item
                    </div>
                    <div class="list-group-item">
                        <i class="bi bi-person me-2"></i>Profile
                    </div>
                </div>
            </div>
            <p><strong>Expected:</strong> Regular items have gray icons, active item has white icons on blue background.</p>
        </div>

        <!-- Statistics Cards Test -->
        <div class="test-section">
            <h3>Statistics Cards Test</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card card-stats bg-primary text-white">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4>150</h4>
                                    <small>Total</small>
                                </div>
                                <div class="avatar-lg bg-white bg-opacity-20">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card card-stats bg-success text-white">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4>89</h4>
                                    <small>Completed</small>
                                </div>
                                <div class="avatar-lg bg-white bg-opacity-20">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card card-stats bg-warning text-white">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4>25</h4>
                                    <small>Pending</small>
                                </div>
                                <div class="avatar-lg bg-white bg-opacity-20">
                                    <i class="bi bi-clock"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card card-stats bg-info text-white">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4>12</h4>
                                    <small>In Progress</small>
                                </div>
                                <div class="avatar-lg bg-white bg-opacity-20">
                                    <i class="bi bi-gear"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p><strong>Expected:</strong> All icons in avatar containers should be white and clearly visible.</p>
        </div>

        <!-- Button Test -->
        <div class="test-section">
            <h3>Button Icons Test</h3>
            <div class="d-flex flex-wrap gap-2">
                <button class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>Primary
                </button>
                <button class="btn btn-success">
                    <i class="bi bi-check-circle me-1"></i>Success
                </button>
                <button class="btn btn-info">
                    <i class="bi bi-info-circle me-1"></i>Info
                </button>
                <button class="btn btn-warning">
                    <i class="bi bi-exclamation-triangle me-1"></i>Warning
                </button>
                <button class="btn btn-danger">
                    <i class="bi bi-x-circle me-1"></i>Danger
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-eye me-1"></i>Outline
                </button>
            </div>
            <p><strong>Expected:</strong> Solid buttons have white icons, outline button has blue icon.</p>
        </div>

        <!-- Card Header Test -->
        <div class="test-section">
            <h3>Card Header Test</h3>
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-file-earmark-text me-2"></i>Card Title with Icon
                    </h5>
                </div>
                <div class="card-body">
                    <p>Card content with proper header icon visibility.</p>
                </div>
            </div>
            <p><strong>Expected:</strong> Icon should be dark and clearly visible on light background.</p>
        </div>

        <!-- Text Utility Classes Test -->
        <div class="test-section">
            <h3>Text Utility Classes Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <p class="text-primary"><i class="bi bi-info-circle me-1"></i>Primary text with icon</p>
                    <p class="text-success"><i class="bi bi-check-circle me-1"></i>Success text with icon</p>
                    <p class="text-info"><i class="bi bi-info-circle me-1"></i>Info text with icon</p>
                    <p class="text-warning"><i class="bi bi-exclamation-triangle me-1"></i>Warning text with icon</p>
                </div>
                <div class="col-md-6">
                    <p class="text-danger"><i class="bi bi-x-circle me-1"></i>Danger text with icon</p>
                    <p class="text-dark"><i class="bi bi-circle me-1"></i>Dark text with icon</p>
                    <p class="text-muted"><i class="bi bi-circle me-1"></i>Muted text with icon</p>
                    <p class="text-secondary"><i class="bi bi-circle me-1"></i>Secondary text with icon</p>
                </div>
            </div>
            <p><strong>Expected:</strong> Icons should match the color of their respective text utility classes.</p>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3>🔍 Test Results</h3>
            <div id="test-results" class="alert alert-info">
                <strong>Running tests...</strong> Please wait while we check icon visibility.
            </div>
            <button class="btn btn-primary" onclick="runColorTests()">Run Color Tests</button>
            <button class="btn btn-secondary" onclick="fixAllColors()">Fix All Colors</button>
        </div>
    </div>

    <script>
        function runColorTests() {
            const results = document.getElementById('test-results');
            let issues = [];
            let passed = 0;
            let total = 0;

            // Test all icons
            document.querySelectorAll('.bi').forEach(icon => {
                total++;
                const computedStyle = window.getComputedStyle(icon);
                const color = computedStyle.color;
                const parent = icon.closest('.navbar-test, .card-stats, .avatar-lg, .btn, .list-group-item');
                
                // Check if color is appropriate for context
                let expectedColor = '';
                let isCorrect = false;

                if (parent) {
                    if (parent.classList.contains('navbar-test')) {
                        expectedColor = 'white';
                        isCorrect = color.includes('255, 255, 255') || color === 'rgb(255, 255, 255)' || color === 'white';
                    } else if (parent.classList.contains('card-stats') || parent.classList.contains('avatar-lg')) {
                        expectedColor = 'white';
                        isCorrect = color.includes('255, 255, 255') || color === 'rgb(255, 255, 255)' || color === 'white';
                    } else if (parent.classList.contains('btn-primary') || parent.classList.contains('btn-success') || 
                               parent.classList.contains('btn-info') || parent.classList.contains('btn-warning') || 
                               parent.classList.contains('btn-danger')) {
                        expectedColor = 'white';
                        isCorrect = color.includes('255, 255, 255') || color === 'rgb(255, 255, 255)' || color === 'white';
                    }
                }

                if (isCorrect) {
                    passed++;
                } else if (expectedColor) {
                    issues.push(`Icon ${icon.className} expected ${expectedColor} but got ${color}`);
                }
            });

            // Display results
            if (issues.length === 0) {
                results.className = 'alert alert-success';
                results.innerHTML = `<strong>✅ All tests passed!</strong> ${passed}/${total} icons have correct colors.`;
            } else {
                results.className = 'alert alert-warning';
                results.innerHTML = `<strong>⚠️ Issues found:</strong> ${passed}/${total} icons correct.<br>` + 
                                   issues.slice(0, 5).join('<br>') + 
                                   (issues.length > 5 ? `<br>...and ${issues.length - 5} more issues.` : '');
            }
        }

        function fixAllColors() {
            // Apply color fixes
            document.querySelectorAll('.navbar-test .bi').forEach(icon => {
                icon.style.color = 'white';
            });

            document.querySelectorAll('.card-stats .bi, .avatar-lg .bi').forEach(icon => {
                icon.style.color = 'white';
            });

            document.querySelectorAll('.btn-primary .bi, .btn-success .bi, .btn-info .bi, .btn-warning .bi, .btn-danger .bi').forEach(icon => {
                icon.style.color = 'white';
            });

            document.querySelectorAll('.btn-outline-primary .bi').forEach(icon => {
                icon.style.color = '#5e72e4';
            });

            document.querySelectorAll('.list-group-item .bi').forEach(icon => {
                const parent = icon.closest('.list-group-item');
                if (parent.classList.contains('active')) {
                    icon.style.color = 'white';
                } else {
                    icon.style.color = '#8392ab';
                }
            });

            // Re-run tests
            setTimeout(runColorTests, 100);
        }

        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                fixAllColors();
                runColorTests();
            }, 500);
        });
    </script>
</body>
</html>
