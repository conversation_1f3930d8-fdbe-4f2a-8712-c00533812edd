<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* Template Color Scheme */
        :root {
            --template-primary-start: #4e54c8;
            --template-primary-end: #8f94fb;
            --template-secondary-start: #6a82fb;
            --template-secondary-end: #fc5c7d;
            --template-background: #f8f9fa;
            --template-white: #ffffff;
            --template-dark: #333333;
            --template-muted: #6c757d;
            --template-light-gray: #e9ecef;
            --template-radius: 15px;
            --template-shadow: 0 6px 15px rgba(0,0,0,0.08);
            --template-shadow-hover: 0 10px 25px rgba(0,0,0,0.12);
        }

        body {
            background-color: var(--template-background);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--template-dark);
        }

        /* Gradient Header */
        .gradient-header {
            background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
            box-shadow: var(--template-shadow);
            text-align: center;
        }

        /* Card Styles */
        .card {
            background: var(--template-white);
            border: none;
            border-radius: var(--template-radius);
            box-shadow: var(--template-shadow);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--template-shadow-hover);
        }

        .card-header {
            background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%);
            border-bottom: none;
            border-radius: var(--template-radius) var(--template-radius) 0 0;
            padding: 1.5rem 2rem;
            color: white;
        }

        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%);
            border: none;
            border-radius: 12px;
            padding: 0.5rem 1.25rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #3d43a8 0%, #7a80db 100%);
            transform: translateY(-2px);
        }

        /* Table Styles */
        .table thead th {
            background-color: var(--template-light-gray);
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: var(--template-dark);
            text-transform: uppercase;
        }

        .table tbody tr:hover {
            background-color: rgba(78, 84, 200, 0.05);
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%);
            color: white;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Gradient Header -->
    <div class="gradient-header">
        <div class="container">
            <i class="bi bi-people-fill d-block" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.8;"></i>
            <h1 class="display-5 mb-3">إدارة المستخدمين</h1>
            <p class="lead">إدارة حسابات المستخدمين وصلاحياتهم في النظام</p>
        </div>
    </div>

    <div class="container">
        <!-- Test Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">اختبار التصميم</h5>
            </div>
            <div class="card-body">
                <p>هذا اختبار للتصميم الجديد</p>
                <button class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مستخدم
                </button>
            </div>
        </div>

        <!-- Test Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">جدول المستخدمين</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar me-3">
                                        <span>AD</span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">admin</div>
                                        <div class="text-muted">مدير النظام</div>
                                    </div>
                                </div>
                            </td>
                            <td><EMAIL></td>
                            <td><span class="badge bg-primary">مدير</span></td>
                            <td><span class="badge bg-success">نشط</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
