#!/usr/bin/env python3
"""
Create demo data with realistic progress tracking for CMSVS Internal System
Run this script to populate the system with sample data optimized for testing progress widgets
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import random

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database import SessionLocal
from app.services.user_service import UserService
from app.services.request_service import RequestService
from app.models.user import UserRole
from app.models.request import RequestStatus
from app.models.activity import ActivityType

def create_test_users(db):
    """Create test users with Arabic names"""
    print("👥 Creating test users...")
    
    test_users = [
        {
            "username": "ahmed_hassan",
            "email": "<EMAIL>",
            "full_name": "أحمد حسن محمد",
            "password": "test123",
            "role": UserRole.USER
        },
        {
            "username": "fatima_ali",
            "email": "<EMAIL>", 
            "full_name": "فاطمة علي أحمد",
            "password": "test123",
            "role": UserRole.USER
        },
        {
            "username": "omar_salem",
            "email": "<EMAIL>",
            "full_name": "عمر سالم عبدالله",
            "password": "test123",
            "role": UserRole.USER
        },
        {
            "username": "sara_mahmoud",
            "email": "<EMAIL>",
            "full_name": "سارة محمود إبراهيم",
            "password": "test123",
            "role": UserRole.USER
        },
        {
            "username": "test_user",
            "email": "<EMAIL>",
            "full_name": "مستخدم تجريبي",
            "password": "test123",
            "role": UserRole.USER
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            # Check if user already exists
            existing_user = UserService.get_user_by_username(db, user_data["username"])
            if existing_user:
                print(f"⚠️  User {user_data['username']} already exists")
                created_users.append(existing_user)
            else:
                user = UserService.create_user(db, **user_data)
                print(f"✅ Created user: {user.username} ({user.full_name})")
                created_users.append(user)
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    return created_users

def create_progress_requests(db, users):
    """Create requests with realistic progress data for testing"""
    print("\n📋 Creating progress tracking demo requests...")
    
    request_templates = [
        {
            "request_name": "أرشفة الوثائق الإدارية",
            "request_title": "أرشفة وثائق الموارد البشرية",
            "description": "طلب أرشفة وثائق الموارد البشرية وفقاً للمعايير المحددة"
        },
        {
            "request_name": "أرشفة العقود",
            "request_title": "أرشفة عقود الموردين والعملاء",
            "description": "أرشفة العقود المبرمة مع الموردين والعملاء"
        },
        {
            "request_name": "أرشفة التقارير المالية",
            "request_title": "أرشفة التقارير المالية الشهرية",
            "description": "أرشفة التقارير المالية وفقاً لمتطلبات المراجعة"
        },
        {
            "request_name": "أرشفة المراسلات",
            "request_title": "أرشفة المراسلات الرسمية",
            "description": "أرشفة المراسلات الرسمية الواردة والصادرة"
        },
        {
            "request_name": "أرشفة الفواتير",
            "request_title": "أرشفة فواتير المشتريات",
            "description": "أرشفة فواتير المشتريات والمصروفات"
        }
    ]
    
    created_requests = []
    
    for user in users:
        print(f"📝 Creating requests for user: {user.full_name}")
        
        # Create completed requests for different time periods
        
        # Daily completed requests (today) - 3 to 8 requests
        daily_count = random.randint(3, 8)
        for i in range(daily_count):
            template = random.choice(request_templates)
            try:
                request = RequestService.create_request(
                    db=db,
                    user_id=user.id,
                    request_name=f"{template['request_name']} - يومي {i+1}",
                    request_title=f"{template['request_title']} - اليوم",
                    description=template['description']
                )
                
                # Set as completed
                RequestService.update_request_status(db, request.id, RequestStatus.COMPLETED)
                
                # Update timestamp to today
                hours_ago = random.randint(1, 10)
                request.created_at = datetime.now() - timedelta(hours=hours_ago)
                db.commit()
                
                created_requests.append(request)
                
            except Exception as e:
                print(f"❌ Error creating daily request: {e}")
        
        # Weekly completed requests (this week) - 20 to 35 requests
        weekly_count = random.randint(20, 35)
        for i in range(weekly_count):
            template = random.choice(request_templates)
            try:
                request = RequestService.create_request(
                    db=db,
                    user_id=user.id,
                    request_name=f"{template['request_name']} - أسبوعي {i+1}",
                    request_title=f"{template['request_title']} - الأسبوع",
                    description=template['description']
                )
                
                # Set as completed
                RequestService.update_request_status(db, request.id, RequestStatus.COMPLETED)
                
                # Update timestamp to this week
                days_ago = random.randint(1, 6)
                hours_ago = random.randint(1, 23)
                request.created_at = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
                db.commit()
                
                created_requests.append(request)
                
            except Exception as e:
                print(f"❌ Error creating weekly request: {e}")
        
        # Monthly completed requests (this month) - 100 to 150 requests
        monthly_count = random.randint(100, 150)
        for i in range(monthly_count):
            template = random.choice(request_templates)
            try:
                request = RequestService.create_request(
                    db=db,
                    user_id=user.id,
                    request_name=f"{template['request_name']} - شهري {i+1}",
                    request_title=f"{template['request_title']} - الشهر",
                    description=template['description']
                )
                
                # Set as completed
                RequestService.update_request_status(db, request.id, RequestStatus.COMPLETED)
                
                # Update timestamp to this month
                days_ago = random.randint(1, 28)
                hours_ago = random.randint(1, 23)
                request.created_at = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
                db.commit()
                
                created_requests.append(request)
                
            except Exception as e:
                print(f"❌ Error creating monthly request: {e}")
        
        # Create some pending and in-progress requests
        for status in [RequestStatus.PENDING, RequestStatus.IN_PROGRESS]:
            count = random.randint(2, 5)
            for i in range(count):
                template = random.choice(request_templates)
                try:
                    request = RequestService.create_request(
                        db=db,
                        user_id=user.id,
                        request_name=f"{template['request_name']} - {status.value} {i+1}",
                        request_title=f"{template['request_title']} - {status.value}",
                        description=template['description']
                    )
                    
                    RequestService.update_request_status(db, request.id, status)
                    created_requests.append(request)
                    
                except Exception as e:
                    print(f"❌ Error creating {status.value} request: {e}")
        
        print(f"✅ Created requests for {user.full_name}: Daily={daily_count}, Weekly={weekly_count}, Monthly={monthly_count}")
    
    print(f"✅ Total requests created: {len(created_requests)}")
    return created_requests

def main():
    """Main function to create progress demo data"""
    print("CMSVS Progress Tracking Demo Data Creation")
    print("=" * 60)
    print("This script creates test users with realistic progress data")
    print("=" * 60)
    print()
    
    db = SessionLocal()
    try:
        # Create test users
        users = create_test_users(db)
        
        if not users:
            print("❌ No users created. Cannot proceed with requests.")
            return
        
        # Create progress tracking demo requests
        requests = create_progress_requests(db, users)
        
        print("\n" + "=" * 60)
        print("🎉 Progress demo data created successfully!")
        print("=" * 60)
        print()
        print("📝 Test User Credentials:")
        print("=" * 30)
        for user in users:
            print(f"👤 Username: {user.username}")
            print(f"   Full Name: {user.full_name}")
            print(f"   Email: {user.email}")
            print(f"   Password: test123")
            print(f"   Role: {user.role.value}")
            print()
        
        print("🚀 Testing Instructions:")
        print("1. Start the application: python run.py")
        print("2. Login with any test user (password: test123)")
        print("3. View personal progress tracking on user dashboard")
        print("4. Login as admin to view all users' progress")
        print()
        print("🎯 Recommended Test User: test_user (password: test123)")
        print("   This user has realistic progress data for testing")
        print()
        
    except Exception as e:
        print(f"❌ Error creating progress demo data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
