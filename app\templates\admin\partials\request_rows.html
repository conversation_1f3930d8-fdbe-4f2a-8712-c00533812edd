{% for req in recent_requests %}
<tr class="border-0">
    <td class="ps-4 py-3">
        <span class="badge bg-light text-dark fw-semibold px-3 py-2">{{ req.request_number }}</span>
    </td>
    <td class="py-3">
        <div class="fw-semibold text-dark">{{ req.request_title }}</div>
    </td>
    <td class="py-3">
        <div class="d-flex align-items-center">
            <div class="avatar-sm bg-secondary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-2">
                <i class="bi bi-person text-white"></i>
            </div>
            <div>
                <div class="fw-semibold text-dark">{{ req.user.full_name }}</div>
                <div class="text-muted small">{{ req.user.email }}</div>
            </div>
        </div>
    </td>
    <td class="py-3">
        {% if req.status.value == 'pending' %}
        <span class="badge bg-warning bg-gradient text-dark fw-semibold px-3 py-2">
            <i class="bi bi-clock me-1"></i>
            قيد المراجعة
        </span>
        {% elif req.status.value == 'in_progress' %}
        <span class="badge bg-info bg-gradient text-white fw-semibold px-3 py-2">
            <i class="bi bi-play-circle me-1"></i>
            قيد التنفيذ
        </span>
        {% elif req.status.value == 'completed' %}
        <span class="badge bg-success bg-gradient text-white fw-semibold px-3 py-2">
            <i class="bi bi-check-circle me-1"></i>
            مكتمل
        </span>
        {% elif req.status.value == 'rejected' %}
        <span class="badge bg-danger bg-gradient text-white fw-semibold px-3 py-2">
            <i class="bi bi-x-circle me-1"></i>
            مرفوض
        </span>
        {% endif %}
    </td>
    <td class="py-3">
        <div class="text-dark fw-semibold">{{ req.created_at.strftime('%Y-%m-%d') }}</div>
        <div class="text-muted small">{{ req.created_at.strftime('%H:%M') }}</div>
    </td>
    <td class="pe-4 py-3">
        <div class="d-flex gap-2">
            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary px-3">
                <i class="bi bi-eye me-1"></i>
                عرض
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle px-3"
                        data-bs-toggle="dropdown">
                    <i class="bi bi-gear me-1"></i>
                    إجراءات
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                            <input type="hidden" name="status" value="in_progress">
                            <button type="submit" class="dropdown-item">
                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                            </button>
                        </form>
                    </li>
                    <li>
                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                            <input type="hidden" name="status" value="completed">
                            <button type="submit" class="dropdown-item">
                                <i class="bi bi-check-circle text-success"></i> مكتمل
                            </button>
                        </form>
                    </li>
                    <li>
                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                            <input type="hidden" name="status" value="rejected">
                            <button type="submit" class="dropdown-item">
                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </td>
</tr>
{% endfor %}

{% if recent_requests %}
<!-- Load More Button Row -->
<tr id="admin-load-more-row" class="border-0">
    <td colspan="6" class="text-center py-4">
        <button 
            class="btn btn-primary btn-sm px-4 py-2 fw-semibold"
            hx-get="/admin/api/requests/load-more?skip={{ next_skip }}"
            hx-target="#admin-requests-tbody"
            hx-swap="beforeend"
            hx-indicator="#admin-loading-indicator"
            onclick="this.closest('tr').remove()">
            <i class="bi bi-arrow-down me-2"></i>
            تحميل المزيد
        </button>
        <div id="admin-loading-indicator" class="htmx-indicator ms-3 d-inline-block">
            <div class="spinner-border spinner-border-sm text-white" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </td>
</tr>
{% endif %}
