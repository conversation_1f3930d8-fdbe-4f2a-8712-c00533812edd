{% extends "base.html" %}

{% block title %}إنشاء مستخدم جديد - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-person-plus"></i>
        إنشاء مستخدم جديد
    </h2>
    <a href="/admin/users" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i>
        العودة لإدارة المستخدمين
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-4">
                <h4 class="mb-1 fw-bold text-dark">
                    <i class="bi bi-person-plus text-primary me-2"></i>
                    بيانات المستخدم الجديد
                </h4>
                <p class="text-muted mb-0 small">قم بإدخال بيانات المستخدم الجديد</p>
            </div>
            <div class="card-body p-4">
                <form method="post" action="/admin/users/new" id="newUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person text-primary me-1"></i>
                                    اسم المستخدم <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ form_data.username if form_data else '' }}" required
                                       pattern="[a-zA-Z0-9_]{3,20}" 
                                       title="3-20 حرف، أرقام وشرطة سفلية فقط"
                                       placeholder="مثال: ahmed_salem">
                                <div class="form-text">3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope text-primary me-1"></i>
                                    البريد الإلكتروني <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ form_data.email if form_data else '' }}" required
                                       placeholder="مثال: <EMAIL>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label fw-semibold">
                                    <i class="bi bi-person-badge text-primary me-1"></i>
                                    الاسم الكامل <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="{{ form_data.full_name if form_data else '' }}" required
                                       placeholder="مثال: أحمد سالم محمد">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label fw-semibold">
                                    <i class="bi bi-shield-check text-primary me-1"></i>
                                    الدور <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">اختر الدور</option>
                                    {% for role_option in roles %}
                                    <option value="{{ role_option }}" 
                                            {% if form_data and form_data.role == role_option %}selected{% endif %}>
                                        {% if role_option == 'user' %}مستخدم عادي
                                        {% elif role_option == 'admin' %}مدير النظام
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <strong>مستخدم عادي:</strong> يمكنه إنشاء وعرض طلباته فقط<br>
                                    <strong>مدير النظام:</strong> يمكنه إدارة جميع المستخدمين والطلبات
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="bi bi-key text-primary me-1"></i>
                                    كلمة المرور <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required
                                           minlength="6" placeholder="أدخل كلمة مرور قوية">
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                        <i class="bi bi-eye" id="passwordIcon"></i>
                                    </button>
                                </div>
                                <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label fw-semibold">
                                    <i class="bi bi-key-fill text-primary me-1"></i>
                                    تأكيد كلمة المرور <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" required
                                       minlength="6" placeholder="أعد إدخال كلمة المرور">
                                <div class="invalid-feedback" id="passwordMismatch">
                                    كلمات المرور غير متطابقة
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-end">
                                <a href="/admin/users" class="btn btn-outline-secondary px-4">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary px-4" id="submitBtn">
                                    <i class="bi bi-person-plus me-2"></i>
                                    إنشاء المستخدم
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const passwordIcon = document.getElementById('passwordIcon');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('newUserForm');

    // Toggle password visibility
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        passwordIcon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
    });

    // Password confirmation validation
    function validatePasswords() {
        const password = passwordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword && password !== confirmPassword) {
            confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
            confirmPasswordField.classList.add('is-invalid');
            return false;
        } else {
            confirmPasswordField.setCustomValidity('');
            confirmPasswordField.classList.remove('is-invalid');
            return true;
        }
    }

    confirmPasswordField.addEventListener('input', validatePasswords);
    passwordField.addEventListener('input', validatePasswords);

    // Form submission validation
    form.addEventListener('submit', function(e) {
        if (!validatePasswords()) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإنشاء...';
        submitBtn.disabled = true;
    });

    // Username validation
    const usernameField = document.getElementById('username');
    usernameField.addEventListener('input', function() {
        const value = this.value;
        const pattern = /^[a-zA-Z0-9_]{3,20}$/;
        
        if (value && !pattern.test(value)) {
            this.setCustomValidity('اسم المستخدم يجب أن يحتوي على 3-20 حرف، أرقام وشرطة سفلية فقط');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
});
</script>
{% endblock %}
