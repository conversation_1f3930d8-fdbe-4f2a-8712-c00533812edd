import os
import uuid
import mimetypes
from typing import List, Optional
from datetime import datetime
from fastapi import UploadFile, HTTPException
from app.config import settings


class FileHandler:
    """Handle file upload operations"""
    
    @staticmethod
    def validate_file(file: UploadFile) -> bool:
        """Validate uploaded file"""
        # Check file size
        if hasattr(file, 'size') and file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
            )
        
        # Check file extension
        if file.filename:
            file_ext = file.filename.split('.')[-1].lower()
            if file_ext not in settings.allowed_file_types_list:
                raise HTTPException(
                    status_code=400,
                    detail=f"File type '{file_ext}' not allowed. Allowed types: {', '.join(settings.allowed_file_types_list)}"
                )
        
        return True
    
    @staticmethod
    def generate_unique_filename(original_filename: str) -> str:
        """Generate unique filename while preserving extension"""
        if not original_filename:
            return f"{uuid.uuid4().hex}.tmp"
        
        name, ext = os.path.splitext(original_filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = uuid.uuid4().hex[:12]
        return f"{timestamp}_{unique_id}{ext}"
    
    @staticmethod
    def get_file_info(file: UploadFile) -> dict:
        """Get file information"""
        mime_type, _ = mimetypes.guess_type(file.filename or "")
        file_ext = ""
        
        if file.filename:
            file_ext = file.filename.split('.')[-1].lower()
        
        return {
            "original_filename": file.filename or "unknown",
            "file_type": file_ext,
            "mime_type": mime_type or "application/octet-stream",
            "content_type": file.content_type or mime_type or "application/octet-stream"
        }
    
    @staticmethod
    async def save_file(file: UploadFile, request_id: int) -> dict:
        """Save uploaded file to disk"""
        # Validate file
        FileHandler.validate_file(file)
        
        # Generate unique filename
        stored_filename = FileHandler.generate_unique_filename(file.filename)
        
        # Create request-specific directory
        request_dir = os.path.join(settings.upload_directory, f"request_{request_id}")
        os.makedirs(request_dir, exist_ok=True)
        
        # Full file path
        file_path = os.path.join(request_dir, stored_filename)
        
        # Save file
        try:
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # Get file info
            file_info = FileHandler.get_file_info(file)
            
            return {
                "original_filename": file_info["original_filename"],
                "stored_filename": stored_filename,
                "file_path": file_path,
                "file_size": len(content),
                "file_type": file_info["file_type"],
                "mime_type": file_info["mime_type"]
            }
            
        except Exception as e:
            # Clean up file if save failed
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to save file: {str(e)}"
            )
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """Delete file from disk"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
    
    @staticmethod
    def validate_file_count(files: List[UploadFile]) -> bool:
        """Validate number of files (max 5 per request)"""
        if len(files) > 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 files allowed per request"
            )
        return True
