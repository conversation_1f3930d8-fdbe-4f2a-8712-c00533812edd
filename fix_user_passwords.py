#!/usr/bin/env python3
"""
Fix user passwords in the CMSVS system
This script will reset passwords for test users to ensure they work correctly
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database import SessionLocal
from app.services.user_service import UserService
from app.utils.auth import get_password_hash
from app.models.user import User, User<PERSON><PERSON>

def fix_user_passwords():
    """Fix passwords for test users"""
    print("🔧 Fixing user passwords...")
    
    db = SessionLocal()
    try:
        # Test users and their correct passwords
        test_users = [
            {"username": "test_user", "password": "password123"},
            {"username": "ahmed_hassan", "password": "password123"},
            {"username": "fatima_ali", "password": "password123"},
            {"username": "omar_salem", "password": "password123"},
            {"username": "sara_mahmoud", "password": "password123"},
        ]
        
        for user_data in test_users:
            username = user_data["username"]
            password = user_data["password"]
            
            # Get user from database
            user = UserService.get_user_by_username(db, username)
            if user:
                # Hash the password properly
                hashed_password = get_password_hash(password)
                
                # Update the user's password
                user.hashed_password = hashed_password
                db.commit()
                
                print(f"✅ Fixed password for user: {username}")
                print(f"   Full Name: {user.full_name}")
                print(f"   Email: {user.email}")
                print(f"   Role: {user.role.value}")
                print(f"   Active: {user.is_active}")
                print(f"   Password: {password}")
                print()
            else:
                print(f"❌ User not found: {username}")
        
        # Also check admin user
        admin_user = UserService.get_user_by_username(db, "admin")
        if admin_user:
            print(f"ℹ️  Admin user found:")
            print(f"   Username: admin")
            print(f"   Full Name: {admin_user.full_name}")
            print(f"   Email: {admin_user.email}")
            print(f"   Role: {admin_user.role.value}")
            print(f"   Active: {admin_user.is_active}")
            print(f"   Password: admin123")
            print()
        
        print("🎉 Password fix completed!")
        print()
        print("📝 Test Credentials:")
        print("=" * 40)
        for user_data in test_users:
            print(f"Username: {user_data['username']}")
            print(f"Password: {user_data['password']}")
            print()
        
        print("🚀 You can now login with these credentials!")
        
    except Exception as e:
        print(f"❌ Error fixing passwords: {e}")
        db.rollback()
    finally:
        db.close()

def verify_user_exists():
    """Verify that test users exist in the database"""
    print("🔍 Verifying test users exist...")
    
    db = SessionLocal()
    try:
        # Check all users in database
        all_users = db.query(User).all()
        
        print(f"📊 Total users in database: {len(all_users)}")
        print()
        
        for user in all_users:
            print(f"👤 Username: {user.username}")
            print(f"   Full Name: {user.full_name}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role.value}")
            print(f"   Active: {user.is_active}")
            print(f"   Created: {user.created_at}")
            print()
        
        # Check specifically for test_user
        test_user = UserService.get_user_by_username(db, "test_user")
        if test_user:
            print("✅ test_user found in database")
            return True
        else:
            print("❌ test_user not found in database")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying users: {e}")
        return False
    finally:
        db.close()

def main():
    """Main function"""
    print("CMSVS User Password Fix Tool")
    print("=" * 50)
    print()
    
    # First verify users exist
    if not verify_user_exists():
        print("❌ Test users not found. Please run create_demo_data.py first.")
        return
    
    # Fix passwords
    fix_user_passwords()

if __name__ == "__main__":
    main()
