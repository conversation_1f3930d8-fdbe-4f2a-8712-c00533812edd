from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, DateTime, Foreign<PERSON><PERSON>, Enum, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum
import uuid


class RequestStatus(enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"


class Request(Base):
    __tablename__ = "requests"

    id = Column(Integer, primary_key=True, index=True)
    request_name = Column(String(200), nullable=False)
    request_title = Column(String(300), nullable=False)
    request_number = Column(String(50), unique=True, index=True, nullable=False)
    unique_code = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text)
    status = Column(Enum(RequestStatus), default=RequestStatus.PENDING, nullable=False)
    is_archived = Column(Boolean, default=False, nullable=False)
    user_id = Column(Inte<PERSON>, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="requests")
    files = relationship("File", back_populates="request", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Request(request_number='{self.request_number}', title='{self.request_title}')>"

    @classmethod
    def generate_request_number(cls) -> str:
        """Generate unique request number"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"REQ-{timestamp}"

    @classmethod
    def generate_unique_code(cls) -> str:
        """Generate unique identification code"""
        return str(uuid.uuid4()).replace("-", "").upper()[:12]
